<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频管理系统测试套件</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #ffd700;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .success {
            background: linear-gradient(45deg, #00b894, #00a085) !important;
        }
        
        .warning {
            background: linear-gradient(45deg, #fdcb6e, #e17055) !important;
        }
        
        .results {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running {
            background: #fdcb6e;
            animation: pulse 1.5s infinite;
        }
        
        .status-success {
            background: #00b894;
        }
        
        .status-error {
            background: #e17055;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00b894, #00a085);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #ffd700;
        }
        
        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 视频管理系统测试套件</h1>
        
        <div class="test-section">
            <h2>🚀 快速测试</h2>
            <div class="button-group">
                <button id="runAllTests">运行完整测试</button>
                <button id="runBasicTests">基础功能测试</button>
                <button id="runPerformanceTests">性能测试</button>
                <button id="clearResults">清空结果</button>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="testStatus">准备就绪</div>
        </div>
        
        <div class="test-grid">
            <div class="test-section">
                <h2>📊 实时指标</h2>
                <div class="metric-card">
                    <div class="metric-value" id="cacheSize">-</div>
                    <div class="metric-label">缓存大小</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memoryUsage">-</div>
                    <div class="metric-label">内存使用 (MB)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="networkType">-</div>
                    <div class="metric-label">网络类型</div>
                </div>
            </div>
            
            <div class="test-section">
                <h2>🎯 页面测试</h2>
                <div class="button-group">
                    <button onclick="testPage('INDEX')">测试首页</button>
                    <button onclick="testPage('MEETINGS')">测试相遇页</button>
                    <button onclick="testPage('ANNIVERSARY')">测试纪念日</button>
                    <button onclick="testPage('MEMORIAL')">测试纪念页</button>
                    <button onclick="testPage('TOGETHER_DAYS')">测试时光轴</button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 测试结果</h2>
            <div class="results" id="testResults">等待测试开始...</div>
        </div>
        
        <div class="test-section">
            <h2>📈 性能监控</h2>
            <div class="button-group">
                <button id="startMonitoring">开始监控</button>
                <button id="stopMonitoring">停止监控</button>
                <button id="generateReport">生成报告</button>
            </div>
            <div class="results" id="performanceResults">性能监控未启动</div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="/config.js"></script>
    <script src="/video-manager.js"></script>
    <script src="/video-test.js"></script>
    <script src="/performance-report.js"></script>

    <script>
        // 测试套件控制器
        class TestSuiteController {
            constructor() {
                this.tester = null;
                this.analyzer = null;
                this.isRunning = false;
                this.init();
            }

            init() {
                // 等待所有脚本加载完成
                setTimeout(() => {
                    this.tester = new VideoManagerTester();
                    this.analyzer = window.performanceAnalyzer;
                    this.setupEventListeners();
                    this.startMetricsUpdate();
                }, 1000);
            }

            setupEventListeners() {
                document.getElementById('runAllTests').addEventListener('click', () => this.runAllTests());
                document.getElementById('runBasicTests').addEventListener('click', () => this.runBasicTests());
                document.getElementById('runPerformanceTests').addEventListener('click', () => this.runPerformanceTests());
                document.getElementById('clearResults').addEventListener('click', () => this.clearResults());
                
                document.getElementById('startMonitoring').addEventListener('click', () => this.startMonitoring());
                document.getElementById('stopMonitoring').addEventListener('click', () => this.stopMonitoring());
                document.getElementById('generateReport').addEventListener('click', () => this.generateReport());
            }

            async runAllTests() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.updateStatus('运行中', 'running');
                this.updateProgress(0);
                
                try {
                    const results = await this.tester.runAllTests();
                    this.displayResults(results);
                    this.updateStatus(`测试完成 - 通过率: ${results.passRate}%`, 
                        results.passRate >= 80 ? 'success' : 'error');
                    this.updateProgress(100);
                } catch (error) {
                    this.displayError('测试执行失败: ' + error.message);
                    this.updateStatus('测试失败', 'error');
                } finally {
                    this.isRunning = false;
                }
            }

            async runBasicTests() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.updateStatus('运行基础测试', 'running');
                
                try {
                    await this.tester.testBasicFunctionality();
                    this.updateStatus('基础测试完成', 'success');
                } catch (error) {
                    this.displayError('基础测试失败: ' + error.message);
                    this.updateStatus('基础测试失败', 'error');
                } finally {
                    this.isRunning = false;
                }
            }

            async runPerformanceTests() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.updateStatus('运行性能测试', 'running');
                
                try {
                    await this.tester.testPerformance();
                    this.updateStatus('性能测试完成', 'success');
                } catch (error) {
                    this.displayError('性能测试失败: ' + error.message);
                    this.updateStatus('性能测试失败', 'error');
                } finally {
                    this.isRunning = false;
                }
            }

            startMonitoring() {
                if (this.analyzer) {
                    this.analyzer.startMonitoring();
                    document.getElementById('performanceResults').textContent = '性能监控已启动...';
                }
            }

            stopMonitoring() {
                if (this.analyzer) {
                    this.analyzer.stopMonitoring();
                    document.getElementById('performanceResults').textContent = '性能监控已停止';
                }
            }

            generateReport() {
                if (this.analyzer) {
                    const report = this.analyzer.printReport();
                    document.getElementById('performanceResults').textContent = JSON.stringify(report, null, 2);
                }
            }

            clearResults() {
                document.getElementById('testResults').textContent = '等待测试开始...';
                document.getElementById('performanceResults').textContent = '性能监控未启动';
                this.updateStatus('准备就绪', '');
                this.updateProgress(0);
            }

            displayResults(results) {
                const resultsDiv = document.getElementById('testResults');
                let output = `测试完成时间: ${new Date().toLocaleString()}\n`;
                output += `总测试数: ${results.total}\n`;
                output += `通过: ${results.passed}\n`;
                output += `失败: ${results.failed}\n`;
                output += `通过率: ${results.passRate}%\n\n`;
                
                output += '详细结果:\n';
                results.tests.forEach(test => {
                    const status = test.result ? '✅' : '❌';
                    output += `${status} ${test.name}: ${test.details}\n`;
                });
                
                resultsDiv.textContent = output;
            }

            displayError(message) {
                document.getElementById('testResults').textContent = message;
            }

            updateStatus(message, type) {
                const statusDiv = document.getElementById('testStatus');
                const indicator = type ? `<span class="status-indicator status-${type}"></span>` : '';
                statusDiv.innerHTML = indicator + message;
            }

            updateProgress(percent) {
                document.getElementById('progressFill').style.width = percent + '%';
            }

            startMetricsUpdate() {
                setInterval(() => {
                    this.updateMetrics();
                }, 2000);
            }

            updateMetrics() {
                if (window.VideoManager) {
                    const status = window.VideoManager.getCacheStatus();
                    document.getElementById('cacheSize').textContent = status.cacheSize;
                    document.getElementById('networkType').textContent = status.networkType;
                    
                    if (performance.memory) {
                        const memory = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                        document.getElementById('memoryUsage').textContent = memory;
                    }
                }
            }
        }

        // 页面测试函数
        async function testPage(pageKey) {
            const config = window.CONFIG?.VIDEOS?.PAGES?.[pageKey];
            if (!config) {
                alert(`页面 ${pageKey} 配置不存在`);
                return;
            }
            
            try {
                console.log(`开始测试页面: ${pageKey}`);
                const startTime = performance.now();
                
                // 模拟页面视频加载测试
                if (window.VideoManager) {
                    await window.VideoManager.preloadVideo(pageKey, config);
                }
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                alert(`页面 ${pageKey} 测试完成\n加载时间: ${duration.toFixed(2)}ms`);
            } catch (error) {
                alert(`页面 ${pageKey} 测试失败: ${error.message}`);
            }
        }

        // 初始化测试套件
        document.addEventListener('DOMContentLoaded', () => {
            new TestSuiteController();
        });
    </script>
</body>
</html>
