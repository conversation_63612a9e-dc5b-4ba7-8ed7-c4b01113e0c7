<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL路由测试 - love.yuh.cool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .url-list {
            list-style: none;
            padding: 0;
        }
        
        .url-item {
            margin: 10px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .url-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            flex: 1;
        }
        
        .url-link:hover {
            text-decoration: underline;
        }
        
        .url-description {
            color: #666;
            font-size: 0.9em;
            margin-left: 15px;
        }
        
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            margin-left: 10px;
        }
        
        .test-button:hover {
            background: #5a6fd8;
        }
        
        .status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status.loading { background: #ffc107; }
        .status.success { background: #28a745; }
        .status.error { background: #dc3545; }
        
        .test-all-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            width: 100%;
            margin: 20px 0;
            transition: transform 0.2s ease;
        }
        
        .test-all-button:hover {
            transform: translateY(-2px);
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Love Website URL路由测试</h1>
        
        <div class="test-section">
            <h3>📍 主要页面路由</h3>
            <ul class="url-list">
                <li class="url-item">
                    <a href="https://love.yuh.cool/" class="url-link" target="_blank">
                        https://love.yuh.cool/
                    </a>
                    <span class="url-description">首页</span>
                    <button class="test-button" onclick="testUrl('https://love.yuh.cool/', 'home')">测试</button>
                    <div class="status loading" id="status-home"></div>
                </li>
                
                <li class="url-item">
                    <a href="https://love.yuh.cool/together-days" class="url-link" target="_blank">
                        https://love.yuh.cool/together-days
                    </a>
                    <span class="url-description">在一起的日子</span>
                    <button class="test-button" onclick="testUrl('https://love.yuh.cool/together-days', 'together-days')">测试</button>
                    <div class="status loading" id="status-together-days"></div>
                </li>
                
                <li class="url-item">
                    <a href="https://love.yuh.cool/anniversary" class="url-link" target="_blank">
                        https://love.yuh.cool/anniversary
                    </a>
                    <span class="url-description">纪念日</span>
                    <button class="test-button" onclick="testUrl('https://love.yuh.cool/anniversary', 'anniversary')">测试</button>
                    <div class="status loading" id="status-anniversary"></div>
                </li>
                
                <li class="url-item">
                    <a href="https://love.yuh.cool/meetings" class="url-link" target="_blank">
                        https://love.yuh.cool/meetings
                    </a>
                    <span class="url-description">相遇回忆</span>
                    <button class="test-button" onclick="testUrl('https://love.yuh.cool/meetings', 'meetings')">测试</button>
                    <div class="status loading" id="status-meetings"></div>
                </li>
                
                <li class="url-item">
                    <a href="https://love.yuh.cool/memorial" class="url-link" target="_blank">
                        https://love.yuh.cool/memorial
                    </a>
                    <span class="url-description">纪念相册</span>
                    <button class="test-button" onclick="testUrl('https://love.yuh.cool/memorial', 'memorial')">测试</button>
                    <div class="status loading" id="status-memorial"></div>
                </li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🧪 测试页面路由</h3>
            <ul class="url-list">
                <li class="url-item">
                    <a href="https://love.yuh.cool/test/test-video-optimization.html" class="url-link" target="_blank">
                        https://love.yuh.cool/test/test-video-optimization.html
                    </a>
                    <span class="url-description">视频性能测试</span>
                    <button class="test-button" onclick="testUrl('https://love.yuh.cool/test/test-video-optimization.html', 'video-test')">测试</button>
                    <div class="status loading" id="status-video-test"></div>
                </li>
                
                <li class="url-item">
                    <a href="https://love.yuh.cool/test/test-api.html" class="url-link" target="_blank">
                        https://love.yuh.cool/test/test-api.html
                    </a>
                    <span class="url-description">API测试</span>
                    <button class="test-button" onclick="testUrl('https://love.yuh.cool/test/test-api.html', 'api-test')">测试</button>
                    <div class="status loading" id="status-api-test"></div>
                </li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>📊 API接口测试</h3>
            <ul class="url-list">
                <li class="url-item">
                    <a href="https://love.yuh.cool/api/health" class="url-link" target="_blank">
                        https://love.yuh.cool/api/health
                    </a>
                    <span class="url-description">健康检查</span>
                    <button class="test-button" onclick="testUrl('https://love.yuh.cool/api/health', 'api-health')">测试</button>
                    <div class="status loading" id="status-api-health"></div>
                </li>
                
                <li class="url-item">
                    <a href="https://love.yuh.cool/api/together-days" class="url-link" target="_blank">
                        https://love.yuh.cool/api/together-days
                    </a>
                    <span class="url-description">在一起天数API</span>
                    <button class="test-button" onclick="testUrl('https://love.yuh.cool/api/together-days', 'api-days')">测试</button>
                    <div class="status loading" id="status-api-days"></div>
                </li>
            </ul>
        </div>
        
        <button class="test-all-button" onclick="testAllUrls()">
            🚀 测试所有URL
        </button>
        
        <div class="results" id="testResults" style="display: none;">
            <strong>测试结果：</strong><br>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        const urls = [
            { url: 'https://love.yuh.cool/', id: 'home', name: '首页' },
            { url: 'https://love.yuh.cool/together-days', id: 'together-days', name: '在一起的日子' },
            { url: 'https://love.yuh.cool/anniversary', id: 'anniversary', name: '纪念日' },
            { url: 'https://love.yuh.cool/meetings', id: 'meetings', name: '相遇回忆' },
            { url: 'https://love.yuh.cool/memorial', id: 'memorial', name: '纪念相册' },
            { url: 'https://love.yuh.cool/test/test-video-optimization.html', id: 'video-test', name: '视频性能测试' },
            { url: 'https://love.yuh.cool/test/test-api.html', id: 'api-test', name: 'API测试' },
            { url: 'https://love.yuh.cool/api/health', id: 'api-health', name: '健康检查API' },
            { url: 'https://love.yuh.cool/api/together-days', id: 'api-days', name: '在一起天数API' }
        ];

        async function testUrl(url, id) {
            const statusElement = document.getElementById(`status-${id}`);
            const resultContent = document.getElementById('resultContent');
            
            statusElement.className = 'status loading';
            
            try {
                const startTime = Date.now();
                const response = await fetch(url, { 
                    method: 'HEAD',
                    mode: 'no-cors' // 避免CORS问题
                });
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                statusElement.className = 'status success';
                
                const message = `✅ ${url} - 响应时间: ${responseTime}ms`;
                console.log(message);
                
                if (resultContent) {
                    resultContent.innerHTML += message + '<br>';
                }
                
            } catch (error) {
                statusElement.className = 'status error';
                
                const message = `❌ ${url} - 错误: ${error.message}`;
                console.log(message);
                
                if (resultContent) {
                    resultContent.innerHTML += message + '<br>';
                }
            }
        }

        async function testAllUrls() {
            const resultsDiv = document.getElementById('testResults');
            const resultContent = document.getElementById('resultContent');
            
            resultsDiv.style.display = 'block';
            resultContent.innerHTML = '开始测试所有URL...<br><br>';
            
            for (const urlInfo of urls) {
                await testUrl(urlInfo.url, urlInfo.id);
                // 添加小延迟避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            resultContent.innerHTML += '<br>🎉 所有URL测试完成！';
        }

        // 页面加载时显示当前时间
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 Love Website URL路由测试页面已加载');
            console.log('📅 当前时间:', new Date().toLocaleString());
        });
    </script>
</body>
</html>
