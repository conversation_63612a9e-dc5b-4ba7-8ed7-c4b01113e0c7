<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频优化测试 - love.yuh.cool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .test-item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .video-container { width: 100%; height: 200px; background: #000; margin: 10px 0; }
        .video-container video { width: 100%; height: 100%; object-fit: cover; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin: 10px 0; }
        .metric { background: #f8f9fa; padding: 10px; text-align: center; border-radius: 5px; }
        .metric-value { font-size: 20px; font-weight: bold; color: #007bff; }
        .log { background: #f8f9fa; padding: 10px; height: 150px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 视频优化测试 - love.yuh.cool</h1>
        
        <div class="test-item">
            <h3>📊 系统信息</h3>
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="networkType">检测中...</div>
                    <div>网络类型</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="networkSpeed">检测中...</div>
                    <div>网络速度</div>
                </div>
            </div>
        </div>

        <div class="test-item">
            <h3>🎥 视频加载测试</h3>
            <button class="btn btn-primary" onclick="testOriginal()">测试原始视频</button>
            <button class="btn btn-primary" onclick="testOptimized()">测试优化视频</button>
            
            <div class="video-container">
                <video id="testVideo" muted loop controls></video>
            </div>
            
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="loadTime">0</div>
                    <div>加载时间(秒)</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="videoFormat">未知</div>
                    <div>视频格式</div>
                </div>
            </div>
        </div>

        <div class="test-item">
            <h3>📝 测试日志</h3>
            <div class="log" id="testLog"></div>
        </div>
    </div>

    <script src="/config.js"></script>
    <script src="/enhanced-video-manager.js"></script>
    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            detectSystemInfo();
            log('🚀 测试页面已加载');
        });

        function detectSystemInfo() {
            if (navigator.connection) {
                document.getElementById('networkType').textContent = navigator.connection.effectiveType || '未知';
                document.getElementById('networkSpeed').textContent = (navigator.connection.downlink || 0) + ' Mbps';
            }
        }

        async function testOriginal() {
            log('🎬 开始测试原始视频...');
            const startTime = performance.now();
            
            try {
                const video = document.getElementById('testVideo');
                video.src = 'https://love.yuh.cool/background/flower-bg.mp4';
                
                await new Promise((resolve, reject) => {
                    video.addEventListener('canplaythrough', resolve, { once: true });
                    video.addEventListener('error', reject, { once: true });
                    video.load();
                });
                
                const loadTime = (performance.now() - startTime) / 1000;
                document.getElementById('loadTime').textContent = loadTime.toFixed(2);
                document.getElementById('videoFormat').textContent = '原始MP4';
                
                log(`✅ 原始视频加载完成，耗时: ${loadTime.toFixed(2)}s`);
                
            } catch (error) {
                log(`❌ 原始视频加载失败: ${error.message}`);
            }
        }

        async function testOptimized() {
            log('🎬 开始测试优化视频...');
            const startTime = performance.now();
            
            try {
                const video = document.getElementById('testVideo');
                video.src = 'https://love.yuh.cool/background/optimized/flower-bg_h265_optimized.mp4';
                
                await new Promise((resolve, reject) => {
                    video.addEventListener('canplaythrough', resolve, { once: true });
                    video.addEventListener('error', reject, { once: true });
                    video.load();
                });
                
                const loadTime = (performance.now() - startTime) / 1000;
                document.getElementById('loadTime').textContent = loadTime.toFixed(2);
                document.getElementById('videoFormat').textContent = 'H.265优化';
                
                log(`✅ 优化视频加载完成，耗时: ${loadTime.toFixed(2)}s`);
                
            } catch (error) {
                log(`❌ 优化视频加载失败，尝试H.264版本...`);
                
                try {
                    video.src = 'https://love.yuh.cool/background/optimized/flower-bg_h264_optimized.mp4';
                    await new Promise((resolve, reject) => {
                        video.addEventListener('canplaythrough', resolve, { once: true });
                        video.addEventListener('error', reject, { once: true });
                        video.load();
                    });
                    
                    const loadTime = (performance.now() - startTime) / 1000;
                    document.getElementById('loadTime').textContent = loadTime.toFixed(2);
                    document.getElementById('videoFormat').textContent = 'H.264优化';
                    
                    log(`✅ H.264优化视频加载完成，耗时: ${loadTime.toFixed(2)}s`);
                    
                } catch (error2) {
                    log(`❌ 优化视频加载失败: ${error2.message}`);
                }
            }
        }

        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
    </script>
</body>
</html>
