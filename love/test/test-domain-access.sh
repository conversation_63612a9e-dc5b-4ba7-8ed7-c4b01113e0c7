#!/bin/bash

# 域名访问测试脚本
# 验证所有页面都能通过 love.yuh.cool 域名正常访问

echo "🌐 Love Website 域名访问测试"
echo "================================"
echo ""

# 定义要测试的URL列表
urls=(
    "https://love.yuh.cool/"
    "https://love.yuh.cool/together-days"
    "https://love.yuh.cool/anniversary"
    "https://love.yuh.cool/meetings"
    "https://love.yuh.cool/memorial"
    "https://love.yuh.cool/test/test-video-loading.html"
    "https://love.yuh.cool/test/test-video-optimization.html"
    "https://love.yuh.cool/test/test-api.html"
    "https://love.yuh.cool/test/test-suite.html"
    "https://love.yuh.cool/background/home/<USER>"
    "https://love.yuh.cool/background/meetings/meetings.mp4"
    "https://love.yuh.cool/background/anniversary/anniversary.mp4"
    "https://love.yuh.cool/background/together-days/together-days.mp4"
    "https://love.yuh.cool/background/memorial/memorial.mp4"
)

# 测试结果统计
total_tests=0
passed_tests=0
failed_tests=0

echo "📋 开始测试..."
echo ""

# 遍历每个URL进行测试
for url in "${urls[@]}"; do
    total_tests=$((total_tests + 1))
    
    echo -n "🔍 测试: $url ... "
    
    # 使用curl测试URL，只获取HTTP状态码
    http_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" --max-time 10)
    
    if [ "$http_code" = "200" ]; then
        echo "✅ 通过 (HTTP $http_code)"
        passed_tests=$((passed_tests + 1))
    else
        echo "❌ 失败 (HTTP $http_code)"
        failed_tests=$((failed_tests + 1))
    fi
done

echo ""
echo "📊 测试结果统计"
echo "================================"
echo "总测试数: $total_tests"
echo "通过数: $passed_tests"
echo "失败数: $failed_tests"
echo ""

if [ $failed_tests -eq 0 ]; then
    echo "🎉 所有测试通过！网站可以正常通过域名访问。"
    echo ""
    echo "✅ 推荐访问方式："
    echo "   - 主页: https://love.yuh.cool/"
    echo "   - 视频测试: https://love.yuh.cool/test/test-video-loading.html"
else
    echo "⚠️ 有 $failed_tests 个测试失败，请检查网站配置。"
fi

echo ""
echo "🔗 重要提醒："
echo "   - 必须使用域名访问: https://love.yuh.cool/"
echo "   - 不要使用端口访问: http://localhost:1314/"
echo "   - 所有测试文件都在 /test/ 路径下"
