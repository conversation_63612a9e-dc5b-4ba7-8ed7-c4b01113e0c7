<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频性能测试 - love.yuh.cool</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .header h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }
        
        .test-section h3 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.4em;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            height: 300px;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .video-overlay {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
        }
        
        .comparison-table tr:hover {
            background: #f8f9fa;
        }
        
        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 20px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-loading { background: #ffc107; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 视频性能测试</h1>
            <p>love.yuh.cool 视频加载优化效果测试</p>
        </div>

        <!-- 系统信息 -->
        <div class="test-section">
            <h3>📊 系统信息检测</h3>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="networkType">检测中...</div>
                    <div class="metric-label">网络类型</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="networkSpeed">检测中...</div>
                    <div class="metric-label">网络速度 (Mbps)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="deviceMemory">检测中...</div>
                    <div class="metric-label">设备内存 (GB)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="deviceCores">检测中...</div>
                    <div class="metric-label">CPU核心数</div>
                </div>
            </div>
        </div>

        <!-- 视频测试 -->
        <div class="test-section">
            <h3>🎥 视频加载性能测试</h3>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="testOriginalVideo()">
                    <span class="status-indicator status-loading" id="originalStatus"></span>
                    测试原始视频
                </button>
                <button class="btn btn-success" onclick="testOptimizedVideo()">
                    <span class="status-indicator status-loading" id="optimizedStatus"></span>
                    测试优化视频
                </button>
                <button class="btn btn-warning" onclick="runComparisonTest()">
                    🔄 完整对比测试
                </button>
            </div>
            
            <div class="video-container">
                <video id="testVideo" muted loop controls>
                    您的浏览器不支持视频播放
                </video>
                <div class="video-overlay" id="videoInfo">准备就绪</div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="loadProgress"></div>
            </div>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="loadTime">0.00</div>
                    <div class="metric-label">加载时间 (秒)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="fileSize">0</div>
                    <div class="metric-label">文件大小 (MB)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="videoFormat">未知</div>
                    <div class="metric-label">视频格式</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="videoQuality">未知</div>
                    <div class="metric-label">视频质量</div>
                </div>
            </div>
        </div>

        <!-- 对比结果 -->
        <div class="test-section">
            <h3>📈 性能对比结果</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>测试项目</th>
                        <th>原始视频</th>
                        <th>优化视频</th>
                        <th>改善程度</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>加载时间</td>
                        <td id="originalTime">-</td>
                        <td id="optimizedTime">-</td>
                        <td id="timeImprovement">-</td>
                        <td id="timeStatus">待测试</td>
                    </tr>
                    <tr>
                        <td>文件大小</td>
                        <td id="originalSize">-</td>
                        <td id="optimizedSize">-</td>
                        <td id="sizeImprovement">-</td>
                        <td id="sizeStatus">待测试</td>
                    </tr>
                    <tr>
                        <td>首帧显示</td>
                        <td id="originalFirstFrame">-</td>
                        <td id="optimizedFirstFrame">-</td>
                        <td id="firstFrameImprovement">-</td>
                        <td id="firstFrameStatus">待测试</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="log-container" id="testLog"></div>
            <div class="controls">
                <button class="btn btn-warning" onclick="clearLog()">清空日志</button>
                <button class="btn btn-success" onclick="exportResults()">导出结果</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let testResults = {
            original: { loadTime: 0, size: 0, firstFrame: 0 },
            optimized: { loadTime: 0, size: 0, firstFrame: 0 }
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            detectSystemInfo();
            log('🚀 视频性能测试页面已加载');
            log('🌐 当前域名: ' + window.location.hostname);
        });

        // 检测系统信息
        function detectSystemInfo() {
            // 网络信息
            if (navigator.connection) {
                const connection = navigator.connection;
                document.getElementById('networkType').textContent = connection.effectiveType || '未知';
                document.getElementById('networkSpeed').textContent = (connection.downlink || 0).toFixed(1);
            } else {
                document.getElementById('networkType').textContent = '未知';
                document.getElementById('networkSpeed').textContent = '未知';
            }

            // 设备信息
            document.getElementById('deviceMemory').textContent = navigator.deviceMemory || '未知';
            document.getElementById('deviceCores').textContent = navigator.hardwareConcurrency || '未知';
        }

        // 测试原始视频
        async function testOriginalVideo() {
            log('🎬 开始测试原始视频 (flower-bg.mp4)...');
            updateStatus('originalStatus', 'loading');

            const startTime = performance.now();
            let firstFrameTime = 0;

            try {
                const video = document.getElementById('testVideo');
                const videoInfo = document.getElementById('videoInfo');

                videoInfo.textContent = '加载原始视频中...';
                updateProgress(0);

                // 监听首帧显示
                video.addEventListener('loadeddata', () => {
                    firstFrameTime = performance.now() - startTime;
                }, { once: true });

                // 模拟加载进度
                const progressInterval = setInterval(() => {
                    const currentProgress = Math.min(90, (performance.now() - startTime) / 100);
                    updateProgress(currentProgress);
                }, 100);

                const videoUrl = '../background/flower-bg.mp4';
                await loadVideo(video, videoUrl);
                
                clearInterval(progressInterval);
                updateProgress(100);
                
                const loadTime = (performance.now() - startTime) / 1000;
                
                // 获取文件大小 (估算)
                const fileSize = 63; // 已知大小
                
                // 更新UI
                document.getElementById('loadTime').textContent = loadTime.toFixed(2);
                document.getElementById('fileSize').textContent = fileSize.toFixed(1);
                document.getElementById('videoFormat').textContent = 'MP4 (原始)';
                document.getElementById('videoQuality').textContent = '2K原始';
                
                videoInfo.textContent = `原始视频 - ${loadTime.toFixed(2)}s`;
                
                // 保存结果
                testResults.original = {
                    loadTime: loadTime,
                    size: fileSize,
                    firstFrame: firstFrameTime / 1000
                };
                
                // 更新对比表
                document.getElementById('originalTime').textContent = loadTime.toFixed(2) + 's';
                document.getElementById('originalSize').textContent = fileSize.toFixed(1) + ' MB';
                document.getElementById('originalFirstFrame').textContent = (firstFrameTime / 1000).toFixed(2) + 's';
                
                updateStatus('originalStatus', 'success');
                log(`✅ 原始视频测试完成 - 加载时间: ${loadTime.toFixed(2)}s, 首帧: ${(firstFrameTime/1000).toFixed(2)}s`);
                
            } catch (error) {
                updateStatus('originalStatus', 'error');
                log(`❌ 原始视频测试失败: ${error.message}`);
                document.getElementById('videoInfo').textContent = '原始视频加载失败';
            }
        }

        // 测试优化视频
        async function testOptimizedVideo() {
            log('🎬 开始测试优化视频...');
            updateStatus('optimizedStatus', 'loading');
            
            const startTime = performance.now();
            let firstFrameTime = 0;
            
            try {
                const video = document.getElementById('testVideo');
                const videoInfo = document.getElementById('videoInfo');
                
                videoInfo.textContent = '加载优化视频中...';
                updateProgress(0);
                
                // 监听首帧显示
                video.addEventListener('loadeddata', () => {
                    firstFrameTime = performance.now() - startTime;
                }, { once: true });
                
                // 尝试不同的优化版本
                const optimizedUrls = [
                    '../background/optimized/flower-bg_h265_optimized.mp4',
                    '../background/optimized/flower-bg_h264_optimized.mp4',
                    '../background/optimized/洱海/DJI_0075_h265_optimized.mp4'
                ];
                
                let loadedUrl = null;
                let format = '未知';
                
                for (const url of optimizedUrls) {
                    try {
                        await loadVideo(video, url);
                        loadedUrl = url;
                        if (url.includes('h265')) format = 'H.265优化';
                        else if (url.includes('h264')) format = 'H.264优化';
                        else format = 'H.265优化';
                        break;
                    } catch (e) {
                        log(`⚠️ 尝试加载 ${url.split('/').pop()} 失败`);
                    }
                }
                
                if (!loadedUrl) {
                    throw new Error('所有优化版本都无法加载');
                }
                
                updateProgress(100);
                
                const loadTime = (performance.now() - startTime) / 1000;
                
                // 估算优化后的文件大小
                const estimatedSize = format.includes('H.265') ? 32 : 38;
                
                // 更新UI
                document.getElementById('loadTime').textContent = loadTime.toFixed(2);
                document.getElementById('fileSize').textContent = estimatedSize.toFixed(1);
                document.getElementById('videoFormat').textContent = format;
                document.getElementById('videoQuality').textContent = '2K优化';
                
                videoInfo.textContent = `${format} - ${loadTime.toFixed(2)}s`;
                
                // 保存结果
                testResults.optimized = {
                    loadTime: loadTime,
                    size: estimatedSize,
                    firstFrame: firstFrameTime / 1000
                };
                
                // 更新对比表
                document.getElementById('optimizedTime').textContent = loadTime.toFixed(2) + 's';
                document.getElementById('optimizedSize').textContent = estimatedSize.toFixed(1) + ' MB';
                document.getElementById('optimizedFirstFrame').textContent = (firstFrameTime / 1000).toFixed(2) + 's';
                
                // 计算改善程度
                if (testResults.original.loadTime > 0) {
                    updateComparison();
                }
                
                updateStatus('optimizedStatus', 'success');
                log(`✅ 优化视频测试完成 - 格式: ${format}, 加载时间: ${loadTime.toFixed(2)}s`);
                
            } catch (error) {
                updateStatus('optimizedStatus', 'error');
                log(`❌ 优化视频测试失败: ${error.message}`);
                log('💡 提示: 请确保已运行视频优化脚本生成优化版本');
                document.getElementById('videoInfo').textContent = '优化视频加载失败';
            }
        }

        // 运行完整对比测试
        async function runComparisonTest() {
            log('🔄 开始完整对比测试...');
            
            await testOriginalVideo();
            await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
            await testOptimizedVideo();
            
            log('🎉 完整对比测试完成！');
        }

        // 更新对比结果
        function updateComparison() {
            const { original, optimized } = testResults;
            
            if (original.loadTime > 0 && optimized.loadTime > 0) {
                const timeImprovement = ((original.loadTime - optimized.loadTime) / original.loadTime * 100);
                const sizeImprovement = ((original.size - optimized.size) / original.size * 100);
                const firstFrameImprovement = ((original.firstFrame - optimized.firstFrame) / original.firstFrame * 100);
                
                document.getElementById('timeImprovement').textContent = 
                    timeImprovement > 0 ? `${timeImprovement.toFixed(1)}% 更快` : `${Math.abs(timeImprovement).toFixed(1)}% 更慢`;
                document.getElementById('sizeImprovement').textContent = 
                    sizeImprovement > 0 ? `${sizeImprovement.toFixed(1)}% 更小` : `${Math.abs(sizeImprovement).toFixed(1)}% 更大`;
                document.getElementById('firstFrameImprovement').textContent = 
                    firstFrameImprovement > 0 ? `${firstFrameImprovement.toFixed(1)}% 更快` : `${Math.abs(firstFrameImprovement).toFixed(1)}% 更慢`;
                
                document.getElementById('timeStatus').textContent = timeImprovement > 0 ? '✅ 改善' : '❌ 退化';
                document.getElementById('sizeStatus').textContent = sizeImprovement > 0 ? '✅ 改善' : '❌ 退化';
                document.getElementById('firstFrameStatus').textContent = firstFrameImprovement > 0 ? '✅ 改善' : '❌ 退化';
            }
        }

        // 辅助函数
        function loadVideo(videoElement, url) {
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('加载超时 (30秒)'));
                }, 30000);

                videoElement.addEventListener('canplaythrough', () => {
                    clearTimeout(timeout);
                    resolve();
                }, { once: true });

                videoElement.addEventListener('error', (error) => {
                    clearTimeout(timeout);
                    reject(new Error('视频加载错误'));
                }, { once: true });

                videoElement.src = url;
                videoElement.load();
            });
        }

        function updateStatus(elementId, status) {
            const element = document.getElementById(elementId);
            element.className = `status-indicator status-${status}`;
        }

        function updateProgress(percent) {
            document.getElementById('loadProgress').style.width = percent + '%';
        }

        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('📝 日志已清空');
        }

        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                domain: window.location.hostname,
                userAgent: navigator.userAgent,
                network: {
                    type: document.getElementById('networkType').textContent,
                    speed: document.getElementById('networkSpeed').textContent
                },
                device: {
                    memory: document.getElementById('deviceMemory').textContent,
                    cores: document.getElementById('deviceCores').textContent
                },
                testResults: testResults
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `video-performance-test-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📊 测试结果已导出');
        }
    </script>
</body>
</html>
