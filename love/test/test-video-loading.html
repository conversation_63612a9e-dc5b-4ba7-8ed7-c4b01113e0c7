<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频加载测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .video-test {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .video-test h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .video-test video {
            width: 100%;
            max-width: 400px;
            height: 200px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .status {
            margin: 10px 0;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎬 视频加载测试</h1>
        <p>测试简化的视频管理器是否能正常加载所有页面的视频背景</p>
        
        <div id="testResults"></div>
        
        <div class="log">
            <h4>控制台日志：</h4>
            <div id="consoleLog"></div>
        </div>
        
        <button onclick="runTests()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            🚀 开始测试
        </button>
    </div>

    <script src="/config.js"></script>
    <script src="/simple-video-manager.js"></script>
    <script>
        const testResults = document.getElementById('testResults');
        const consoleLog = document.getElementById('consoleLog');
        
        // 劫持console.log来显示日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const logEntry = document.createElement('div');
            logEntry.textContent = args.join(' ');
            consoleLog.appendChild(logEntry);
            consoleLog.scrollTop = consoleLog.scrollHeight;
        };
        
        const videoConfigs = [
            { key: 'INDEX', name: '首页花朵背景', config: window.CONFIG?.VIDEOS?.PAGES?.INDEX },
            { key: 'MEETINGS', name: '相遇回忆星河背景', config: window.CONFIG?.VIDEOS?.PAGES?.MEETINGS },
            { key: 'ANNIVERSARY', name: '纪念日绿荫背景', config: window.CONFIG?.VIDEOS?.PAGES?.ANNIVERSARY },
            { key: 'MEMORIAL', name: '纪念相册海洋背景', config: window.CONFIG?.VIDEOS?.PAGES?.MEMORIAL },
            { key: 'TOGETHER_DAYS', name: '在一起的日子海底背景', config: window.CONFIG?.VIDEOS?.PAGES?.TOGETHER_DAYS }
        ];
        
        async function runTests() {
            testResults.innerHTML = '';
            consoleLog.innerHTML = '';
            
            console.log('🚀 开始视频加载测试...');
            
            for (const { key, name, config } of videoConfigs) {
                await testVideoLoad(key, name, config);
            }
            
            console.log('✅ 所有测试完成！');
        }
        
        async function testVideoLoad(key, name, config) {
            const testDiv = document.createElement('div');
            testDiv.className = 'video-test';
            testDiv.innerHTML = `
                <h3>${name}</h3>
                <div class="status loading" id="status-${key}">⏳ 加载中...</div>
                <video id="video-${key}" muted></video>
            `;
            testResults.appendChild(testDiv);
            
            const statusDiv = document.getElementById(`status-${key}`);
            const videoElement = document.getElementById(`video-${key}`);
            
            try {
                if (!config) {
                    throw new Error('配置不存在');
                }
                
                console.log(`🎬 测试 ${name} (${key})`);
                console.log(`📹 视频URL: ${config.url}`);
                
                // 使用简单视频管理器加载视频
                const video = await window.VideoManager.loadVideo(key, config);
                
                // 显示加载的视频
                videoElement.src = video.src;
                videoElement.load();
                
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 加载成功';
                
                console.log(`✅ ${name} 加载成功`);
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ 加载失败: ${error.message}`;
                
                console.error(`❌ ${name} 加载失败:`, error);
            }
        }
        
        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📦 页面加载完成，配置检查:');
            console.log('CONFIG:', window.CONFIG ? '✅ 已加载' : '❌ 未加载');
            console.log('VideoManager:', window.VideoManager ? '✅ 已加载' : '❌ 未加载');
            
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
