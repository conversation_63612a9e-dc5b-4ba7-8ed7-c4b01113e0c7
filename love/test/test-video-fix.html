<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频修复测试 - Love Website</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0,0,0,0.7);
            padding: 30px;
            border-radius: 15px;
        }
        
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            background: rgba(255,255,255,0.1);
        }
        
        .error {
            border-left-color: #f44336;
        }
        
        .success {
            border-left-color: #4CAF50;
        }
        
        .warning {
            border-left-color: #ff9800;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        #console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 视频背景修复测试</h1>
        <p>测试 VideoManager 和 EnhancedVideoManager 的兼容性修复</p>
        
        <div class="test-section">
            <h3>📋 测试项目</h3>
            <button onclick="testVideoManagerExists()">1. 测试 VideoManager 是否存在</button>
            <button onclick="testEnhancedVideoManagerExists()">2. 测试 EnhancedVideoManager 是否存在</button>
            <button onclick="testVideoManagerMethods()">3. 测试 VideoManager 方法</button>
            <button onclick="testConfigExists()">4. 测试配置文件</button>
            <button onclick="testVideoLoad()">5. 测试视频加载</button>
            <button onclick="clearConsole()">清空控制台</button>
        </div>
        
        <div id="test-results"></div>
        <div id="console-output"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="/config.js"></script>
    <script src="/enhanced-video-manager.js"></script>
    
    <script>
        // 控制台输出重定向
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f44336' : type === 'warn' ? '#ff9800' : '#4CAF50';
            consoleOutput.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        // 测试结果显示
        function showResult(title, success, message) {
            const resultsDiv = document.getElementById('test-results');
            const resultClass = success ? 'success' : 'error';
            const icon = success ? '✅' : '❌';
            
            resultsDiv.innerHTML += `
                <div class="test-result ${resultClass}">
                    <strong>${icon} ${title}</strong><br>
                    ${message}
                </div>
            `;
        }
        
        // 测试函数
        function testVideoManagerExists() {
            const exists = typeof window.VideoManager !== 'undefined';
            showResult(
                'VideoManager 存在性测试',
                exists,
                exists ? 'window.VideoManager 已定义' : 'window.VideoManager 未定义'
            );
            console.log('VideoManager exists:', exists);
            if (exists) {
                console.log('VideoManager type:', typeof window.VideoManager);
                console.log('VideoManager constructor:', window.VideoManager.constructor.name);
            }
        }
        
        function testEnhancedVideoManagerExists() {
            const exists = typeof window.EnhancedVideoManager !== 'undefined';
            showResult(
                'EnhancedVideoManager 存在性测试',
                exists,
                exists ? 'window.EnhancedVideoManager 已定义' : 'window.EnhancedVideoManager 未定义'
            );
            console.log('EnhancedVideoManager exists:', exists);
        }
        
        function testVideoManagerMethods() {
            if (typeof window.VideoManager === 'undefined') {
                showResult('VideoManager 方法测试', false, 'VideoManager 不存在');
                return;
            }
            
            const requiredMethods = ['loadVideo', 'loadVideoForPage', 'preloadVideo', 'pauseAllVideos', 'resumeCurrentVideo'];
            const missingMethods = [];
            
            requiredMethods.forEach(method => {
                if (typeof window.VideoManager[method] !== 'function') {
                    missingMethods.push(method);
                }
            });
            
            const success = missingMethods.length === 0;
            showResult(
                'VideoManager 方法测试',
                success,
                success ? '所有必需方法都存在' : `缺少方法: ${missingMethods.join(', ')}`
            );
            
            console.log('Available methods:', Object.getOwnPropertyNames(window.VideoManager).filter(name => typeof window.VideoManager[name] === 'function'));
        }
        
        function testConfigExists() {
            const configExists = typeof window.CONFIG !== 'undefined';
            const videosConfigExists = configExists && typeof window.CONFIG.VIDEOS !== 'undefined';
            const pagesConfigExists = videosConfigExists && typeof window.CONFIG.VIDEOS.PAGES !== 'undefined';
            
            showResult(
                '配置文件测试',
                pagesConfigExists,
                pagesConfigExists ? '配置文件完整' : '配置文件缺失或不完整'
            );
            
            if (configExists) {
                console.log('CONFIG exists');
                console.log('CONFIG.VIDEOS exists:', videosConfigExists);
                console.log('CONFIG.VIDEOS.PAGES exists:', pagesConfigExists);
                if (pagesConfigExists) {
                    console.log('Available pages:', Object.keys(window.CONFIG.VIDEOS.PAGES));
                }
            }
        }
        
        async function testVideoLoad() {
            if (typeof window.VideoManager === 'undefined') {
                showResult('视频加载测试', false, 'VideoManager 不存在');
                return;
            }
            
            if (typeof window.CONFIG === 'undefined' || !window.CONFIG.VIDEOS?.PAGES?.INDEX) {
                showResult('视频加载测试', false, '视频配置不存在');
                return;
            }
            
            try {
                console.log('开始测试视频加载...');
                const videoConfig = window.CONFIG.VIDEOS.PAGES.INDEX;
                console.log('使用配置:', videoConfig);
                
                // 测试 loadVideo 方法
                const video = await window.VideoManager.loadVideo('INDEX', videoConfig);
                
                showResult(
                    '视频加载测试',
                    !!video,
                    video ? '视频加载成功' : '视频加载失败'
                );
                
                if (video) {
                    console.log('视频元素创建成功:', video.tagName);
                    console.log('视频源:', video.src);
                }
                
            } catch (error) {
                showResult('视频加载测试', false, `加载失败: ${error.message}`);
                console.error('视频加载错误:', error);
            }
        }
        
        function clearConsole() {
            document.getElementById('console-output').innerHTML = '';
            document.getElementById('test-results').innerHTML = '';
        }
        
        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 视频修复测试页面已加载');
            console.log('开始自动测试...');
            
            setTimeout(() => {
                testVideoManagerExists();
                testEnhancedVideoManagerExists();
                testConfigExists();
            }, 1000);
        });
    </script>
</body>
</html>
