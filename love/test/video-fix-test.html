<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频管理器修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #ffd700;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .success {
            background: linear-gradient(45deg, #00b894, #00a085) !important;
        }
        
        .warning {
            background: linear-gradient(45deg, #fdcb6e, #e17055) !important;
        }
        
        .results {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .status-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #ffd700;
        }
        
        .status-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .navigation-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .navigation-links a {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .navigation-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 视频管理器修复测试</h1>
        
        <div class="test-section">
            <h2>🚀 快速测试</h2>
            <div class="button-group">
                <button onclick="testVideoManager()">测试VideoManager</button>
                <button onclick="testPageDetection()">测试页面检测</button>
                <button onclick="testCacheSystem()">测试缓存系统</button>
                <button onclick="testVideoResume()">测试视频恢复</button>
                <button onclick="clearConsole()">清空控制台</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📊 系统状态</h2>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-value" id="initStatus">检查中...</div>
                    <div class="status-label">初始化状态</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="cacheSize">-</div>
                    <div class="status-label">缓存大小</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="currentPage">-</div>
                    <div class="status-label">当前页面</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="networkType">-</div>
                    <div class="status-label">网络类型</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔗 页面导航测试</h2>
            <p>点击以下链接测试页面切换和视频缓存：</p>
            <div class="navigation-links">
                <a href="index.html">首页 (花朵)</a>
                <a href="meetings.html">相遇页 (星河)</a>
                <a href="anniversary.html">纪念日 (绿荫)</a>
                <a href="memorial.html">纪念页 (海洋)</a>
                <a href="together-days.html">时光轴 (湖泊)</a>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 测试结果</h2>
            <div class="results" id="testResults">等待测试开始...</div>
        </div>
        
        <div class="test-section">
            <h2>🐛 问题诊断</h2>
            <div class="button-group">
                <button onclick="diagnoseVideoIssues()">诊断视频问题</button>
                <button onclick="checkCacheConsistency()">检查缓存一致性</button>
                <button onclick="testNetworkHandling()">测试网络处理</button>
                <button onclick="simulatePageSwitch()">模拟页面切换</button>
            </div>
            <div class="results" id="diagnosticResults">诊断结果将显示在这里...</div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="/config.js"></script>
    <script src="/video-manager.js"></script>
    <script src="/video-init.js"></script>

    <script>
        // 测试控制器
        class VideoFixTester {
            constructor() {
                this.init();
            }

            init() {
                // 等待所有脚本加载完成
                setTimeout(() => {
                    this.updateStatus();
                    this.startStatusMonitoring();
                }, 1000);
            }

            updateStatus() {
                // 检查初始化状态
                const initStatus = window.VIDEO_MANAGER_INITIALIZED ? '✅ 已初始化' : '❌ 未初始化';
                document.getElementById('initStatus').textContent = initStatus;

                // 检查VideoManager状态
                if (window.VideoManager) {
                    const status = window.VideoManager.getCacheStatus();
                    document.getElementById('cacheSize').textContent = status.cacheSize;
                    document.getElementById('networkType').textContent = status.networkType;
                    document.getElementById('currentPage').textContent = window.VideoManager.getCurrentPageKey();
                } else {
                    document.getElementById('cacheSize').textContent = 'N/A';
                    document.getElementById('networkType').textContent = 'N/A';
                    document.getElementById('currentPage').textContent = 'N/A';
                }
            }

            startStatusMonitoring() {
                setInterval(() => {
                    this.updateStatus();
                }, 2000);
            }

            log(message) {
                const resultsDiv = document.getElementById('testResults');
                const timestamp = new Date().toLocaleTimeString();
                resultsDiv.textContent += `[${timestamp}] ${message}\n`;
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
            }

            logDiagnostic(message) {
                const resultsDiv = document.getElementById('diagnosticResults');
                const timestamp = new Date().toLocaleTimeString();
                resultsDiv.textContent += `[${timestamp}] ${message}\n`;
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
            }
        }

        // 测试函数
        function testVideoManager() {
            const tester = window.videoFixTester;
            tester.log('开始测试VideoManager...');
            
            if (!window.VideoManager) {
                tester.log('❌ VideoManager未初始化');
                return;
            }
            
            tester.log('✅ VideoManager已存在');
            tester.log(`当前页面: ${window.VideoManager.getCurrentPageKey()}`);
            tester.log(`当前主题: ${window.VideoManager.getCurrentTheme()}`);
            tester.log(`缓存大小: ${window.VideoManager.getCacheStatus().cacheSize}`);
            tester.log('VideoManager测试完成');
        }

        function testPageDetection() {
            const tester = window.videoFixTester;
            tester.log('开始测试页面检测...');
            
            if (!window.VideoManager) {
                tester.log('❌ VideoManager未初始化');
                return;
            }
            
            const pageKey = window.VideoManager.getCurrentPageKey();
            const theme = window.VideoManager.getCurrentTheme();
            const config = window.CONFIG?.VIDEOS?.PAGES?.[pageKey];
            
            tester.log(`检测到页面: ${pageKey}`);
            tester.log(`对应主题: ${theme}`);
            tester.log(`配置存在: ${config ? '✅' : '❌'}`);
            
            if (config) {
                tester.log(`视频文件: ${config.file}`);
                tester.log(`视频URL: ${config.url}`);
            }
            
            tester.log('页面检测测试完成');
        }

        function testCacheSystem() {
            const tester = window.videoFixTester;
            tester.log('开始测试缓存系统...');
            
            if (!window.VideoManager) {
                tester.log('❌ VideoManager未初始化');
                return;
            }
            
            const status = window.VideoManager.getCacheStatus();
            tester.log(`缓存大小: ${status.cacheSize}/${status.maxCacheSize}`);
            tester.log(`缓存的视频: ${status.cachedVideos.join(', ')}`);
            
            // 测试缓存查找性能
            const startTime = performance.now();
            for (let i = 0; i < 1000; i++) {
                window.VideoManager.videoCache.has('INDEX');
            }
            const endTime = performance.now();
            tester.log(`缓存查找性能: 1000次查找耗时${(endTime - startTime).toFixed(2)}ms`);
            
            tester.log('缓存系统测试完成');
        }

        function testVideoResume() {
            const tester = window.videoFixTester;
            tester.log('开始测试视频恢复...');
            
            if (!window.VideoManager) {
                tester.log('❌ VideoManager未初始化');
                return;
            }
            
            tester.log('暂停所有视频...');
            window.VideoManager.pauseAllVideos();
            
            setTimeout(() => {
                tester.log('尝试恢复当前视频...');
                window.VideoManager.resumeCurrentVideo();
                tester.log('视频恢复测试完成');
            }, 1000);
        }

        function diagnoseVideoIssues() {
            const tester = window.videoFixTester;
            tester.logDiagnostic('开始诊断视频问题...');
            
            // 检查基本环境
            tester.logDiagnostic(`CONFIG存在: ${!!window.CONFIG}`);
            tester.logDiagnostic(`VideoManager存在: ${!!window.VideoManager}`);
            tester.logDiagnostic(`初始化状态: ${window.VIDEO_MANAGER_INITIALIZED}`);
            
            if (window.VideoManager) {
                const currentPageKey = window.VideoManager.getCurrentPageKey();
                const cachedVideo = window.VideoManager.videoCache.get(currentPageKey);
                
                tester.logDiagnostic(`当前页面: ${currentPageKey}`);
                tester.logDiagnostic(`缓存视频存在: ${!!cachedVideo}`);
                
                if (cachedVideo) {
                    tester.logDiagnostic(`视频就绪状态: ${cachedVideo.readyState}`);
                    tester.logDiagnostic(`视频网络状态: ${cachedVideo.networkState}`);
                    tester.logDiagnostic(`视频暂停状态: ${cachedVideo.paused}`);
                    tester.logDiagnostic(`视频在DOM中: ${!!cachedVideo.parentNode}`);
                    tester.logDiagnostic(`视频透明度: ${cachedVideo.style.opacity}`);
                    tester.logDiagnostic(`视频错误: ${cachedVideo.error ? cachedVideo.error.message : '无'}`);
                }
            }
            
            tester.logDiagnostic('视频问题诊断完成');
        }

        function checkCacheConsistency() {
            const tester = window.videoFixTester;
            tester.logDiagnostic('检查缓存一致性...');
            
            if (!window.VideoManager) {
                tester.logDiagnostic('❌ VideoManager未初始化');
                return;
            }
            
            const cache = window.VideoManager.videoCache;
            const loadingQueue = window.VideoManager.loadingQueue;
            
            tester.logDiagnostic(`缓存条目数: ${cache.size}`);
            tester.logDiagnostic(`加载队列数: ${loadingQueue.size}`);
            
            // 检查每个缓存条目
            cache.forEach((video, key) => {
                const inDOM = !!video.parentNode;
                const hasError = !!video.error;
                tester.logDiagnostic(`${key}: DOM=${inDOM}, Error=${hasError}, ReadyState=${video.readyState}`);
            });
            
            tester.logDiagnostic('缓存一致性检查完成');
        }

        function testNetworkHandling() {
            const tester = window.videoFixTester;
            tester.logDiagnostic('测试网络处理...');
            
            if (!window.VideoManager) {
                tester.logDiagnostic('❌ VideoManager未初始化');
                return;
            }
            
            const networkType = window.VideoManager.networkType;
            const preloadStrategy = window.VideoManager.getPreloadStrategy();
            
            tester.logDiagnostic(`网络类型: ${networkType}`);
            tester.logDiagnostic(`预加载策略: ${preloadStrategy}`);
            
            // 模拟网络连接检测
            if (navigator.connection) {
                tester.logDiagnostic(`实际网络类型: ${navigator.connection.effectiveType}`);
                tester.logDiagnostic(`下行速度: ${navigator.connection.downlink}Mbps`);
            }
            
            tester.logDiagnostic('网络处理测试完成');
        }

        function simulatePageSwitch() {
            const tester = window.videoFixTester;
            tester.logDiagnostic('模拟页面切换...');
            
            if (!window.VideoManager) {
                tester.logDiagnostic('❌ VideoManager未初始化');
                return;
            }
            
            // 模拟页面隐藏
            tester.logDiagnostic('模拟页面隐藏...');
            window.VideoManager.pauseAllVideos();
            
            setTimeout(() => {
                // 模拟页面显示
                tester.logDiagnostic('模拟页面显示...');
                window.VideoManager.resumeCurrentVideo();
                tester.logDiagnostic('页面切换模拟完成');
            }, 2000);
        }

        function clearConsole() {
            document.getElementById('testResults').textContent = '控制台已清空...\n';
            document.getElementById('diagnosticResults').textContent = '诊断结果已清空...\n';
        }

        // 初始化测试器
        document.addEventListener('DOMContentLoaded', () => {
            window.videoFixTester = new VideoFixTester();
        });
    </script>
</body>
</html>
