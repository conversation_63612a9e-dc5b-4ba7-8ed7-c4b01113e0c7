/**
 * Video Manager Initialization Script
 * 视频管理器初始化脚本 - 解决重复初始化和页面切换问题
 */

(function() {
    'use strict';

    // 全局初始化状态
    window.VIDEO_MANAGER_INITIALIZED = window.VIDEO_MANAGER_INITIALIZED || false;

    /**
     * 初始化视频管理器
     */
    function initVideoManager() {
        // 防止重复初始化
        if (window.VIDEO_MANAGER_INITIALIZED) {
            console.log('🎬 VideoManager 已初始化，恢复当前页面视频');
            
            // 如果VideoManager已存在，直接恢复当前页面视频
            if (window.VideoManager) {
                const currentPageKey = window.VideoManager.getCurrentPageKey();
                window.VideoManager.loadVideoForPage(currentPageKey);
            }
            return;
        }

        // 等待配置文件和VideoManager加载完成
        const checkDependencies = () => {
            if (window.CONFIG && window.VideoManager) {
                console.log('🎬 开始初始化视频管理器');
                
                // 标记为已初始化
                window.VIDEO_MANAGER_INITIALIZED = true;
                
                // 获取当前页面配置
                const currentPageKey = window.VideoManager.getCurrentPageKey();
                const config = window.CONFIG.VIDEOS.PAGES[currentPageKey];
                
                if (config) {
                    console.log(`🎯 当前页面: ${currentPageKey}，开始加载视频`);
                    window.VideoManager.loadVideoForPage(currentPageKey);
                } else {
                    console.log(`❌ 未找到页面配置: ${currentPageKey}`);
                    window.VideoManager.applyFallbackBackground(window.VideoManager.getCurrentTheme());
                }
                
                // 设置页面可见性变化监听
                setupVisibilityChangeHandler();
                
                // 设置页面卸载处理
                setupUnloadHandler();
                
            } else {
                // 依赖未加载完成，继续等待
                setTimeout(checkDependencies, 100);
            }
        };
        
        checkDependencies();
    }

    /**
     * 设置页面可见性变化处理
     */
    function setupVisibilityChangeHandler() {
        if (document.visibilityState !== undefined) {
            document.addEventListener('visibilitychange', function() {
                if (window.VideoManager) {
                    if (document.hidden) {
                        console.log('📱 页面隐藏，暂停所有视频');
                        window.VideoManager.pauseAllVideos();
                    } else {
                        console.log('📱 页面显示，恢复当前视频');
                        window.VideoManager.resumeCurrentVideo();
                    }
                }
            });
        }
    }

    /**
     * 设置页面卸载处理（优化版本）
     */
    function setupUnloadHandler() {
        // 页面卸载时不清理缓存，只暂停视频
        window.addEventListener('beforeunload', function() {
            if (window.VideoManager) {
                console.log('🚪 页面即将卸载，暂停视频播放');
                window.VideoManager.pauseAllVideos();
                // 不清理缓存，保持视频资源用于下次访问
            }
        });

        // 页面隐藏时暂停视频
        window.addEventListener('pagehide', function() {
            if (window.VideoManager) {
                console.log('🚪 页面隐藏，暂停视频播放');
                window.VideoManager.pauseAllVideos();
            }
        });

        // 页面显示时恢复视频
        window.addEventListener('pageshow', function(event) {
            if (window.VideoManager) {
                console.log('🚪 页面显示，恢复视频播放');
                // 如果是从缓存中恢复的页面
                if (event.persisted) {
                    console.log('📄 从页面缓存恢复');
                }
                window.VideoManager.resumeCurrentVideo();
            }
        });
    }

    /**
     * 智能初始化 - 根据页面加载状态选择合适的初始化时机
     */
    function smartInit() {
        if (document.readyState === 'loading') {
            // 页面还在加载中
            document.addEventListener('DOMContentLoaded', initVideoManager);
        } else {
            // 页面已经加载完成
            initVideoManager();
        }
    }

    // 导出全局初始化函数
    window.initVideoManager = initVideoManager;
    window.smartInitVideoManager = smartInit;

    // 自动初始化
    smartInit();

    console.log('🎬 Video Init Script 已加载');

})();
