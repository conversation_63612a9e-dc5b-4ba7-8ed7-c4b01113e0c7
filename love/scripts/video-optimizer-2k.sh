#!/bin/bash

# Video Optimization Script for Love Website
# 保持2K质量的同时优化文件大小和加载速度

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SOURCE_DIR="./background"
OUTPUT_DIR="./background/optimized"
TEMP_DIR="./temp_optimization"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"
mkdir -p "$TEMP_DIR"

echo -e "${BLUE}🎬 Love Website Video Optimizer (保持2K质量)${NC}"
echo -e "${BLUE}============================================${NC}"

# 检查ffmpeg是否安装
if ! command -v ffmpeg &> /dev/null; then
    echo -e "${RED}❌ FFmpeg未安装，请先安装FFmpeg${NC}"
    echo "Ubuntu/Debian: sudo apt install ffmpeg"
    echo "macOS: brew install ffmpeg"
    exit 1
fi

# 检查H.265支持
if ! ffmpeg -encoders 2>/dev/null | grep -q libx265; then
    echo -e "${YELLOW}⚠️ 未检测到H.265编码器，将使用H.264优化编码${NC}"
    USE_H265=false
else
    echo -e "${GREEN}✅ 检测到H.265编码器支持${NC}"
    USE_H265=true
fi

# 优化函数 - 保持原始分辨率和质量
optimize_video() {
    local input_file="$1"
    local output_dir="$2"
    local filename=$(basename "$input_file")
    local name="${filename%.*}"
    local relative_path=$(dirname "${input_file#$SOURCE_DIR/}")
    
    # 创建相对路径的输出目录
    local target_dir="$output_dir/$relative_path"
    mkdir -p "$target_dir"
    
    echo -e "${YELLOW}📹 优化: $filename${NC}"
    
    # 获取原始文件信息
    local original_size=$(du -h "$input_file" | cut -f1)
    echo -e "   原始大小: $original_size"
    
    # 获取视频信息
    local video_info=$(ffprobe -v quiet -print_format json -show_streams "$input_file")
    local width=$(echo "$video_info" | grep -o '"width":[0-9]*' | cut -d':' -f2)
    local height=$(echo "$video_info" | grep -o '"height":[0-9]*' | cut -d':' -f2)
    
    echo -e "   原始分辨率: ${width}x${height}"
    
    if [ "$USE_H265" = true ]; then
        # H.265优化版本 - 保持原始分辨率
        echo -e "   🔄 生成H.265优化版本 (保持${width}x${height})..."
        ffmpeg -i "$input_file" \
            -c:v libx265 \
            -preset medium \
            -crf 20 \
            -pix_fmt yuv420p \
            -c:a aac -b:a 128k \
            -movflags +faststart \
            -tag:v hvc1 \
            -y "$target_dir/${name}_h265_optimized.mp4" 2>/dev/null
    fi
    
    # H.264优化版本 - 保持原始分辨率，使用两遍编码
    echo -e "   🔄 生成H.264优化版本 (保持${width}x${height})..."
    
    # 计算合适的码率 (基于分辨率)
    local target_bitrate
    if [ "$width" -ge 2560 ]; then
        target_bitrate="8000k"  # 2K+
    elif [ "$width" -ge 1920 ]; then
        target_bitrate="5000k"  # 1080p
    else
        target_bitrate="3000k"  # 其他
    fi
    
    # 两遍编码以获得最佳质量/大小比
    ffmpeg -i "$input_file" \
        -c:v libx264 \
        -preset slow \
        -b:v "$target_bitrate" \
        -maxrate "$target_bitrate" \
        -bufsize $((${target_bitrate%k} * 2))k \
        -pix_fmt yuv420p \
        -c:a aac -b:a 128k \
        -movflags +faststart \
        -pass 1 -f null /dev/null 2>/dev/null && \
    ffmpeg -i "$input_file" \
        -c:v libx264 \
        -preset slow \
        -b:v "$target_bitrate" \
        -maxrate "$target_bitrate" \
        -bufsize $((${target_bitrate%k} * 2))k \
        -pix_fmt yuv420p \
        -c:a aac -b:a 128k \
        -movflags +faststart \
        -pass 2 \
        -y "$target_dir/${name}_h264_optimized.mp4" 2>/dev/null
    
    # 清理两遍编码的临时文件
    rm -f ffmpeg2pass-*.log
    
    # WebM版本 (VP9) - 保持原始分辨率
    echo -e "   🔄 生成WebM版本 (保持${width}x${height})..."
    ffmpeg -i "$input_file" \
        -c:v libvpx-vp9 \
        -crf 23 \
        -b:v 0 \
        -pix_fmt yuv420p \
        -c:a libopus -b:a 128k \
        -y "$target_dir/${name}_vp9_optimized.webm" 2>/dev/null
    
    # 显示优化结果
    echo -e "${GREEN}   ✅ 优化完成:${NC}"
    
    if [ "$USE_H265" = true ] && [ -f "$target_dir/${name}_h265_optimized.mp4" ]; then
        local h265_size=$(du -h "$target_dir/${name}_h265_optimized.mp4" | cut -f1)
        echo -e "      H.265: $h265_size"
    fi
    
    if [ -f "$target_dir/${name}_h264_optimized.mp4" ]; then
        local h264_size=$(du -h "$target_dir/${name}_h264_optimized.mp4" | cut -f1)
        echo -e "      H.264: $h264_size"
    fi
    
    if [ -f "$target_dir/${name}_vp9_optimized.webm" ]; then
        local webm_size=$(du -h "$target_dir/${name}_vp9_optimized.webm" | cut -f1)
        echo -e "      WebM: $webm_size"
    fi
    
    echo ""
}

# 查找并优化所有视频文件
echo -e "${BLUE}🔍 搜索视频文件...${NC}"
find "$SOURCE_DIR" -type f \( -iname "*.mp4" -o -iname "*.MP4" \) | while read -r video_file; do
    # 跳过已经优化的文件
    if [[ "$video_file" == *"/optimized/"* ]]; then
        continue
    fi
    
    optimize_video "$video_file" "$OUTPUT_DIR"
done

echo -e "${GREEN}🎉 所有视频优化完成！${NC}"
echo -e "${BLUE}优化后的文件保存在: $OUTPUT_DIR${NC}"

# 生成优化报告
echo -e "${BLUE}📊 生成优化报告...${NC}"
{
    echo "# Video Optimization Report (保持2K质量)"
    echo "Generated on: $(date)"
    echo ""
    echo "## 优化策略"
    echo "- 保持原始分辨率 (2K/1080p等)"
    echo "- 使用H.265/H.264高效编码"
    echo "- 优化码率和编码参数"
    echo "- 添加faststart支持"
    echo ""
    echo "## Original Files"
    find "$SOURCE_DIR" -type f \( -iname "*.mp4" -o -iname "*.MP4" \) | while read -r file; do
        if [[ "$file" != *"/optimized/"* ]]; then
            size=$(du -h "$file" | cut -f1)
            echo "- $(basename "$file"): $size"
        fi
    done
    echo ""
    echo "## Optimized Files"
    find "$OUTPUT_DIR" -type f \( -iname "*.mp4" -o -iname "*.webm" \) | while read -r file; do
        size=$(du -h "$file" | cut -f1)
        echo "- $(basename "$file"): $size"
    done
} > "$OUTPUT_DIR/optimization_report.md"

echo -e "${GREEN}✅ 优化报告已生成: $OUTPUT_DIR/optimization_report.md${NC}"

# 清理临时文件
rm -rf "$TEMP_DIR"

echo -e "${BLUE}🚀 下一步：更新配置文件以使用优化后的视频${NC}"
echo -e "${BLUE}💡 建议：优先使用H.265版本以获得最佳文件大小${NC}"
