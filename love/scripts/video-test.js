/**
 * 视频管理系统集成测试脚本
 * 用于验证VideoManager的各项功能和性能
 */

(function() {
    'use strict';

    // 测试结果收集器
    const TestResults = {
        tests: [],
        passed: 0,
        failed: 0,
        
        add: function(name, result, details = '') {
            this.tests.push({
                name,
                result,
                details,
                timestamp: new Date().toISOString()
            });
            
            if (result) {
                this.passed++;
                console.log(`✅ ${name}: PASSED ${details}`);
            } else {
                this.failed++;
                console.log(`❌ ${name}: FAILED ${details}`);
            }
        },
        
        summary: function() {
            const total = this.passed + this.failed;
            const passRate = ((this.passed / total) * 100).toFixed(1);
            
            console.log('\n📊 测试结果汇总:');
            console.log(`总测试数: ${total}`);
            console.log(`通过: ${this.passed}`);
            console.log(`失败: ${this.failed}`);
            console.log(`通过率: ${passRate}%`);
            
            return {
                total,
                passed: this.passed,
                failed: this.failed,
                passRate: parseFloat(passRate),
                tests: this.tests
            };
        }
    };

    // 性能监控器
    const PerformanceMonitor = {
        metrics: {},
        
        start: function(name) {
            this.metrics[name] = {
                startTime: performance.now(),
                startMemory: this.getMemoryUsage()
            };
        },
        
        end: function(name) {
            if (!this.metrics[name]) return null;
            
            const endTime = performance.now();
            const endMemory = this.getMemoryUsage();
            
            const result = {
                duration: endTime - this.metrics[name].startTime,
                memoryDelta: endMemory - this.metrics[name].startMemory,
                startMemory: this.metrics[name].startMemory,
                endMemory: endMemory
            };
            
            console.log(`⏱️ ${name}: ${result.duration.toFixed(2)}ms, 内存变化: ${result.memoryDelta.toFixed(2)}MB`);
            return result;
        },
        
        getMemoryUsage: function() {
            if (performance.memory) {
                return performance.memory.usedJSHeapSize / 1024 / 1024; // MB
            }
            return 0;
        }
    };

    // 网络状况模拟器
    const NetworkSimulator = {
        originalConnection: null,
        
        init: function() {
            this.originalConnection = navigator.connection;
        },
        
        simulate: function(type) {
            // 模拟不同网络类型
            const mockConnection = {
                effectiveType: type,
                downlink: type === '4g' ? 10 : type === '3g' ? 1.5 : 0.5
            };
            
            // 临时替换navigator.connection
            Object.defineProperty(navigator, 'connection', {
                value: mockConnection,
                configurable: true
            });
        },
        
        restore: function() {
            if (this.originalConnection) {
                Object.defineProperty(navigator, 'connection', {
                    value: this.originalConnection,
                    configurable: true
                });
            }
        }
    };

    // 主测试类
    class VideoManagerTester {
        constructor() {
            this.videoManager = null;
            this.testPages = ['INDEX', 'MEETINGS', 'ANNIVERSARY', 'MEMORIAL', 'TOGETHER_DAYS'];
        }

        async runAllTests() {
            console.log('🚀 开始视频管理系统集成测试...\n');
            
            try {
                // 1. 基础功能测试
                await this.testBasicFunctionality();
                
                // 2. 缓存机制测试
                await this.testCacheMechanism();
                
                // 3. 网络适应性测试
                await this.testNetworkAdaptability();
                
                // 4. 性能测试
                await this.testPerformance();
                
                // 5. 错误处理测试
                await this.testErrorHandling();
                
                // 6. 移动端兼容性测试
                await this.testMobileCompatibility();
                
                // 7. 内存管理测试
                await this.testMemoryManagement();
                
            } catch (error) {
                console.error('测试执行出错:', error);
                TestResults.add('测试执行', false, error.message);
            }
            
            return TestResults.summary();
        }

        async testBasicFunctionality() {
            console.log('📋 1. 基础功能测试');
            
            // 测试VideoManager初始化
            try {
                this.videoManager = window.VideoManager;
                TestResults.add('VideoManager初始化', !!this.videoManager, 'VideoManager实例存在');
            } catch (error) {
                TestResults.add('VideoManager初始化', false, error.message);
                return;
            }
            
            // 测试配置加载
            try {
                const config = window.CONFIG?.VIDEOS;
                TestResults.add('配置文件加载', !!config, '视频配置存在');
                
                // 测试页面配置完整性
                const pages = config?.PAGES;
                const hasAllPages = this.testPages.every(page => pages && pages[page]);
                TestResults.add('页面配置完整性', hasAllPages, `${this.testPages.length}个页面配置完整`);
                
            } catch (error) {
                TestResults.add('配置文件加载', false, error.message);
            }
            
            // 测试网络检测
            try {
                const networkType = this.videoManager.detectNetworkType();
                TestResults.add('网络检测功能', !!networkType, `检测到网络类型: ${networkType}`);
            } catch (error) {
                TestResults.add('网络检测功能', false, error.message);
            }
        }

        async testCacheMechanism() {
            console.log('\n💾 2. 缓存机制测试');
            
            if (!this.videoManager) return;
            
            try {
                // 清空缓存开始测试
                this.videoManager.clearAllCache();
                
                // 测试缓存状态
                let status = this.videoManager.getCacheStatus();
                TestResults.add('缓存初始状态', status.cacheSize === 0, `缓存大小: ${status.cacheSize}`);
                
                // 模拟视频加载到缓存
                const testConfig = window.CONFIG.VIDEOS.PAGES.INDEX;
                if (testConfig) {
                    PerformanceMonitor.start('视频缓存加载');
                    
                    // 这里我们模拟缓存操作，因为实际加载需要视频文件
                    const mockVideo = document.createElement('video');
                    mockVideo.classList.add('loaded');
                    this.videoManager.addToCache('INDEX', mockVideo);
                    
                    PerformanceMonitor.end('视频缓存加载');
                    
                    status = this.videoManager.getCacheStatus();
                    TestResults.add('视频缓存添加', status.cacheSize === 1, `缓存大小: ${status.cacheSize}`);
                }
                
            } catch (error) {
                TestResults.add('缓存机制测试', false, error.message);
            }
        }

        async testNetworkAdaptability() {
            console.log('\n📶 3. 网络适应性测试');
            
            if (!this.videoManager) return;
            
            NetworkSimulator.init();
            
            try {
                // 测试不同网络类型下的策略
                const networkTypes = ['4g', '3g', '2g'];
                
                for (const type of networkTypes) {
                    NetworkSimulator.simulate(type);
                    
                    const detectedType = this.videoManager.detectNetworkType();
                    const strategy = this.videoManager.getPreloadStrategy();
                    
                    TestResults.add(
                        `${type.toUpperCase()}网络检测`,
                        detectedType !== 'unknown',
                        `检测类型: ${detectedType}, 策略: ${strategy}`
                    );
                }
                
            } catch (error) {
                TestResults.add('网络适应性测试', false, error.message);
            } finally {
                NetworkSimulator.restore();
            }
        }

        async testPerformance() {
            console.log('\n⚡ 4. 性能测试');
            
            if (!this.videoManager) return;
            
            try {
                // 测试缓存查找性能
                PerformanceMonitor.start('缓存查找性能');
                
                for (let i = 0; i < 1000; i++) {
                    this.videoManager.getCacheStatus();
                }
                
                const cachePerf = PerformanceMonitor.end('缓存查找性能');
                TestResults.add('缓存查找性能', cachePerf.duration < 100, `1000次查找耗时: ${cachePerf.duration.toFixed(2)}ms`);
                
                // 测试内存使用
                const memoryUsage = PerformanceMonitor.getMemoryUsage();
                TestResults.add('内存使用检测', memoryUsage > 0, `当前内存使用: ${memoryUsage.toFixed(2)}MB`);
                
            } catch (error) {
                TestResults.add('性能测试', false, error.message);
            }
        }

        async testErrorHandling() {
            console.log('\n🛡️ 5. 错误处理测试');
            
            if (!this.videoManager) return;
            
            try {
                // 测试无效配置处理
                try {
                    await this.videoManager.loadVideo('INVALID_PAGE', null);
                    TestResults.add('无效配置处理', false, '应该抛出错误但没有');
                } catch (error) {
                    TestResults.add('无效配置处理', true, '正确抛出错误');
                }
                
                // 测试备用背景应用
                try {
                    this.videoManager.applyFallbackBackground('flower');
                    TestResults.add('备用背景应用', true, '备用背景应用成功');
                } catch (error) {
                    TestResults.add('备用背景应用', false, error.message);
                }
                
            } catch (error) {
                TestResults.add('错误处理测试', false, error.message);
            }
        }

        async testMobileCompatibility() {
            console.log('\n📱 6. 移动端兼容性测试');
            
            try {
                // 模拟移动端环境
                const originalUserAgent = navigator.userAgent;
                Object.defineProperty(navigator, 'userAgent', {
                    value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
                    configurable: true
                });
                
                // 测试移动端特定功能
                const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
                TestResults.add('移动端检测', isMobile, '成功检测移动端环境');
                
                // 恢复原始userAgent
                Object.defineProperty(navigator, 'userAgent', {
                    value: originalUserAgent,
                    configurable: true
                });
                
            } catch (error) {
                TestResults.add('移动端兼容性测试', false, error.message);
            }
        }

        async testMemoryManagement() {
            console.log('\n🧠 7. 内存管理测试');
            
            if (!this.videoManager) return;
            
            try {
                const initialMemory = PerformanceMonitor.getMemoryUsage();
                
                // 模拟大量缓存操作
                for (let i = 0; i < 10; i++) {
                    const mockVideo = document.createElement('video');
                    mockVideo.classList.add('loaded');
                    this.videoManager.addToCache(`TEST_${i}`, mockVideo);
                }
                
                const afterCacheMemory = PerformanceMonitor.getMemoryUsage();
                
                // 清理缓存
                this.videoManager.clearAllCache();
                
                const afterCleanMemory = PerformanceMonitor.getMemoryUsage();
                
                const memoryRecovered = afterCacheMemory > afterCleanMemory;
                TestResults.add('内存清理效果', memoryRecovered, 
                    `缓存前: ${initialMemory.toFixed(2)}MB, 缓存后: ${afterCacheMemory.toFixed(2)}MB, 清理后: ${afterCleanMemory.toFixed(2)}MB`);
                
            } catch (error) {
                TestResults.add('内存管理测试', false, error.message);
            }
        }
    }

    // 导出测试器到全局
    window.VideoManagerTester = VideoManagerTester;
    window.TestResults = TestResults;
    window.PerformanceMonitor = PerformanceMonitor;

    // 自动运行测试（如果在测试环境中）
    if (window.location.search.includes('test=video')) {
        document.addEventListener('DOMContentLoaded', async function() {
            const tester = new VideoManagerTester();
            const results = await tester.runAllTests();
            
            // 将结果输出到页面
            const resultDiv = document.createElement('div');
            resultDiv.id = 'test-results';
            resultDiv.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 20px;
                border-radius: 8px;
                font-family: monospace;
                font-size: 12px;
                max-width: 400px;
                max-height: 80vh;
                overflow-y: auto;
                z-index: 10000;
            `;
            
            resultDiv.innerHTML = `
                <h3>视频管理系统测试结果</h3>
                <p>总测试数: ${results.total}</p>
                <p>通过: ${results.passed}</p>
                <p>失败: ${results.failed}</p>
                <p>通过率: ${results.passRate}%</p>
                <button onclick="this.parentElement.remove()">关闭</button>
            `;
            
            document.body.appendChild(resultDiv);
        });
    }

    console.log('🧪 视频管理系统测试脚本已加载');
    console.log('💡 使用方法:');
    console.log('   const tester = new VideoManagerTester();');
    console.log('   tester.runAllTests().then(results => console.log(results));');
    console.log('   或在URL中添加 ?test=video 自动运行测试');

})();
