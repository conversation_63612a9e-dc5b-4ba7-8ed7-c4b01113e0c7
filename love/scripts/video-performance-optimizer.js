/**
 * Video Performance Optimizer - 视频性能优化器
 * 全面优化所有视频背景的加载性能和用户体验
 */

(function() {
    'use strict';

    class VideoPerformanceOptimizer {
        constructor() {
            this.loadingProgress = new Map(); // 加载进度跟踪
            this.performanceMetrics = new Map(); // 性能指标
            this.compressionSettings = new Map(); // 压缩设置
            this.adaptiveQuality = true; // 自适应质量
            this.init();
        }

        init() {
            console.log('🚀 VideoPerformanceOptimizer 初始化');
            this.setupPerformanceMonitoring();
            this.setupAdaptiveQuality();
            this.setupProgressTracking();
        }

        /**
         * 设置性能监控
         */
        setupPerformanceMonitoring() {
            // 监控网络状况
            if (navigator.connection) {
                navigator.connection.addEventListener('change', () => {
                    this.handleNetworkChange();
                });
            }

            // 监控内存使用
            if (performance.memory) {
                setInterval(() => {
                    this.monitorMemoryUsage();
                }, 5000);
            }
        }

        /**
         * 处理网络变化
         */
        handleNetworkChange() {
            const connection = navigator.connection;
            console.log(`📶 网络状况变化: ${connection.effectiveType}, ${connection.downlink}Mbps`);
            
            // 根据网络状况调整视频质量
            if (connection.effectiveType === '2g' || connection.downlink < 1) {
                this.switchToLowQuality();
            } else if (connection.effectiveType === '4g' && connection.downlink > 10) {
                this.switchToHighQuality();
            }
        }

        /**
         * 切换到低质量模式
         */
        switchToLowQuality() {
            console.log('📉 切换到低质量模式');
            if (window.VideoManager) {
                // 暂停所有预加载
                window.VideoManager.preloadQueue = [];
                
                // 降低视频质量设置
                this.applyQualitySettings('low');
            }
        }

        /**
         * 切换到高质量模式
         */
        switchToHighQuality() {
            console.log('📈 切换到高质量模式');
            if (window.VideoManager) {
                this.applyQualitySettings('high');
            }
        }

        /**
         * 应用质量设置
         */
        applyQualitySettings(quality) {
            const settings = {
                low: {
                    preload: 'none',
                    maxConcurrent: 1,
                    timeout: 60000,
                    retries: 1
                },
                medium: {
                    preload: 'metadata',
                    maxConcurrent: 2,
                    timeout: 30000,
                    retries: 2
                },
                high: {
                    preload: 'auto',
                    maxConcurrent: 3,
                    timeout: 20000,
                    retries: 3
                }
            };

            const config = settings[quality] || settings.medium;
            console.log(`⚙️ 应用${quality}质量设置:`, config);
            
            // 更新VideoManager设置
            if (window.VideoManager) {
                window.VideoManager.qualitySettings = config;
            }
        }

        /**
         * 监控内存使用
         */
        monitorMemoryUsage() {
            if (!performance.memory) return;

            const memory = performance.memory;
            const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
            const totalMB = (memory.totalJSHeapSize / 1024 / 1024).toFixed(1);
            const limitMB = (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(1);

            // 如果内存使用超过80%，触发清理
            const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
            if (usagePercent > 80) {
                console.log(`⚠️ 内存使用过高: ${usagePercent.toFixed(1)}%`);
                this.triggerMemoryCleanup();
            }

            this.performanceMetrics.set('memory', {
                used: usedMB,
                total: totalMB,
                limit: limitMB,
                usagePercent: usagePercent.toFixed(1)
            });
        }

        /**
         * 触发内存清理
         */
        triggerMemoryCleanup() {
            if (window.VideoManager) {
                console.log('🧹 触发内存清理');
                
                // 清理未使用的缓存
                window.VideoManager.cleanupUnusedCache();
                
                // 强制垃圾回收（如果支持）
                if (window.gc) {
                    window.gc();
                }
            }
        }

        /**
         * 设置自适应质量
         */
        setupAdaptiveQuality() {
            // 检测设备性能
            const deviceInfo = this.detectDeviceCapability();
            console.log('📱 设备性能检测:', deviceInfo);

            // 根据设备性能调整默认质量
            if (deviceInfo.isLowEnd) {
                this.switchToLowQuality();
            } else if (deviceInfo.isHighEnd) {
                this.switchToHighQuality();
            } else {
                this.applyQualitySettings('medium');
            }
        }

        /**
         * 检测设备性能
         */
        detectDeviceCapability() {
            const info = {
                cores: navigator.hardwareConcurrency || 2,
                memory: navigator.deviceMemory || 4,
                connection: navigator.connection?.effectiveType || '4g',
                isLowEnd: false,
                isHighEnd: false
            };

            // 判断是否为低端设备
            info.isLowEnd = (
                info.cores <= 2 || 
                info.memory <= 2 || 
                info.connection === '2g' ||
                /Android.*4\.|iPhone.*OS [5-8]_/.test(navigator.userAgent)
            );

            // 判断是否为高端设备
            info.isHighEnd = (
                info.cores >= 8 && 
                info.memory >= 8 && 
                info.connection === '4g'
            );

            return info;
        }

        /**
         * 设置进度跟踪
         */
        setupProgressTracking() {
            // 创建进度显示元素
            this.createProgressIndicator();
        }

        /**
         * 创建进度指示器
         */
        createProgressIndicator() {
            const progressContainer = document.createElement('div');
            progressContainer.id = 'video-loading-progress';
            progressContainer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: rgba(255, 255, 255, 0.2);
                z-index: 9999;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            const progressBar = document.createElement('div');
            progressBar.id = 'video-progress-bar';
            progressBar.style.cssText = `
                height: 100%;
                background: linear-gradient(90deg, #ff6b6b, #ffd700);
                width: 0%;
                transition: width 0.3s ease;
            `;

            progressContainer.appendChild(progressBar);
            document.body.appendChild(progressContainer);

            // 添加加载文本
            const loadingText = document.createElement('div');
            loadingText.id = 'video-loading-text';
            loadingText.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px 40px;
                border-radius: 10px;
                font-family: 'Segoe UI', sans-serif;
                font-size: 16px;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
                backdrop-filter: blur(10px);
            `;
            loadingText.textContent = '正在加载视频背景...';
            document.body.appendChild(loadingText);
        }

        /**
         * 显示加载进度
         */
        showLoadingProgress(pageKey, progress) {
            const progressContainer = document.getElementById('video-loading-progress');
            const progressBar = document.getElementById('video-progress-bar');
            const loadingText = document.getElementById('video-loading-text');

            if (progressContainer && progressBar && loadingText) {
                progressContainer.style.opacity = '1';
                loadingText.style.opacity = '1';
                progressBar.style.width = `${progress}%`;
                
                const config = window.CONFIG?.VIDEOS?.PAGES?.[pageKey];
                const videoName = config?.name || pageKey;
                loadingText.textContent = `正在加载 ${videoName}... ${progress}%`;

                // 完成时隐藏
                if (progress >= 100) {
                    setTimeout(() => {
                        progressContainer.style.opacity = '0';
                        loadingText.style.opacity = '0';
                    }, 500);
                }
            }
        }

        /**
         * 优化视频元素
         */
        optimizeVideoElement(video, pageKey) {
            // 根据页面优先级设置不同的优化策略
            const config = window.CONFIG?.VIDEOS?.PAGES?.[pageKey];
            const priority = config?.priority || 999;

            if (priority === 1) {
                // 首页视频 - 最高优先级
                video.preload = 'auto';
                video.style.willChange = 'opacity';
            } else if (priority <= 3) {
                // 重要页面 - 中等优先级
                video.preload = 'metadata';
            } else {
                // 普通页面 - 低优先级
                video.preload = 'none';
            }

            // 添加性能优化属性
            video.style.transform = 'translateZ(0)'; // 启用硬件加速
            video.style.backfaceVisibility = 'hidden';
            video.style.perspective = '1000px';

            return video;
        }

        /**
         * 获取性能报告
         */
        getPerformanceReport() {
            return {
                metrics: Object.fromEntries(this.performanceMetrics),
                loadingProgress: Object.fromEntries(this.loadingProgress),
                timestamp: new Date().toISOString()
            };
        }

        /**
         * 清理资源
         */
        cleanup() {
            // 清理进度指示器
            const progressContainer = document.getElementById('video-loading-progress');
            const loadingText = document.getElementById('video-loading-text');
            
            if (progressContainer) progressContainer.remove();
            if (loadingText) loadingText.remove();
            
            console.log('🧹 VideoPerformanceOptimizer 资源已清理');
        }
    }

    // 创建全局实例
    if (!window.VideoPerformanceOptimizer) {
        window.VideoPerformanceOptimizer = new VideoPerformanceOptimizer();
        console.log('🚀 VideoPerformanceOptimizer 已初始化');
    }

    // 导出到全局
    window.videoPerformanceOptimizer = window.VideoPerformanceOptimizer;

})();
