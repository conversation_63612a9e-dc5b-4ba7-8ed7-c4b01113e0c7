#!/bin/bash

# 💕 Love Site 专用部署脚本
# 解决视频背景问题并完整部署Love情侣网站

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目路径
LOVE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$(dirname "$LOVE_DIR")"

# 打印横幅
print_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    💕 Love Site 专用部署系统 💕               ║"
    echo "║                                                              ║"
    echo "║  🎬 解决视频背景加载问题                                    ║"
    echo "║  🌐 情侣专属网站完整部署                                    ║"
    echo "║  📦 包含Git LFS处理和备用背景                               ║"
    echo "║  🔧 优化的Nginx配置和SSL证书管理                            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
}

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
    exit 1
}

log_check_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_check_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_check_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查Love项目环境..."
    
    # 重新确定LOVE_DIR路径
    LOVE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 检查是否在正确的目录
    if [ ! -f "$LOVE_DIR/server.js" ]; then
        log_error "找不到Love Site项目文件: $LOVE_DIR/server.js"
    fi
    
    # 检查manage.sh是否存在
    if [ ! -f "$LOVE_DIR/manage.sh" ]; then
        log_error "找不到manage.sh管理脚本: $LOVE_DIR/manage.sh"
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js"
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装，请先安装npm"
    fi
    
    log_success "环境检查通过"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "检查和安装系统依赖..."

    # 检查并安装Git LFS
    if ! command -v git-lfs &> /dev/null; then
        log_info "安装Git LFS..."
        if command -v apt &> /dev/null; then
            apt update
            apt install -y git-lfs
        elif command -v yum &> /dev/null; then
            yum install -y git-lfs
        else
            log_warning "无法自动安装Git LFS，请手动安装"
        fi

        if command -v git-lfs &> /dev/null; then
            log_success "Git LFS安装成功"
        else
            log_warning "Git LFS安装失败，将使用备用背景"
        fi
    else
        log_check_success "Git LFS已安装"
    fi

    # 检查ffmpeg（用于视频处理，可选）
    if ! command -v ffmpeg &> /dev/null; then
        log_info "安装FFmpeg（用于视频处理）..."
        if command -v apt &> /dev/null; then
            apt install -y ffmpeg
        elif command -v yum &> /dev/null; then
            yum install -y ffmpeg
        fi

        if command -v ffmpeg &> /dev/null; then
            log_success "FFmpeg安装成功"
        else
            log_warning "FFmpeg安装失败，但不影响基本功能"
        fi
    else
        log_check_success "FFmpeg已安装"
    fi
}

# 安装项目依赖
install_dependencies() {
    log_info "安装项目依赖..."

    cd "$LOVE_DIR"

    if [ ! -d "node_modules" ]; then
        log_info "安装npm依赖..."
        npm install
        log_success "依赖安装完成"
    else
        log_info "依赖已存在，跳过安装"
    fi
}

# 处理视频背景文件
handle_video_backgrounds() {
    log_info "处理视频背景文件..."

    cd "$LOVE_DIR"

    if [[ ! -d "background" ]]; then
        log_warning "背景目录不存在，跳过视频背景处理"
        return
    fi

    local has_real_videos=false
    local lfs_files=()
    local total_video_files=0

    # 检查所有视频文件 - 简化处理避免中文文件名问题
    local video_files=$(find background -name "*.mp4" -o -name "*.webm" -o -name "*.ogg" -o -name "*.MP4" 2>/dev/null | wc -l)
    total_video_files=$video_files

    log_info "找到 $total_video_files 个视频文件"

    # 检查是否有Git LFS文件
    local lfs_count=0
    if [[ $total_video_files -gt 0 ]]; then
        # 检查第一个文件作为示例
        local sample_file=$(find background -name "*.mp4" -o -name "*.MP4" 2>/dev/null | head -1)
        if [[ -f "$sample_file" ]]; then
            local file_size=$(stat -c%s "$sample_file" 2>/dev/null || echo "0")
            if [[ $file_size -lt 1000 ]] && grep -q "git-lfs\|version https://git-lfs" "$sample_file" 2>/dev/null; then
                lfs_count=$total_video_files
                log_check_warning "检测到Git LFS指针文件: $(basename "$sample_file") (${file_size}字节)"
                log_warning "所有视频文件都是Git LFS指针文件"
            elif [[ $file_size -gt 1000000 ]]; then
                has_real_videos=true
                log_check_success "检测到真实视频文件: $(basename "$sample_file") ($(numfmt --to=iec $file_size))"
            fi
        fi
    fi

    log_info "视频文件统计: 总计 $total_video_files 个，Git LFS文件 $lfs_count 个"

    # 如果有Git LFS文件，尝试下载
    if [[ $lfs_count -gt 0 ]]; then
        log_info "发现 $lfs_count 个Git LFS文件，尝试下载..."

        if command -v git-lfs &> /dev/null; then
            # 初始化Git LFS
            if git lfs install 2>/dev/null; then
                log_info "Git LFS初始化成功"
            else
                log_warning "Git LFS初始化失败"
            fi

            # 设置GitHub令牌进行认证
            export GIT_LFS_SKIP_SMUDGE=0

            # 配置Git凭据
            git config --global credential.helper store
            echo "https://<EMAIL>" > ~/.git-credentials

            # 尝试下载LFS文件
            log_info "正在下载Git LFS文件，这可能需要几分钟..."
            if timeout 300 git lfs pull 2>/dev/null; then
                log_success "Git LFS文件下载成功"
                has_real_videos=true

                # 重新检查一个示例文件
                local sample_file=$(find background -name "*.mp4" -o -name "*.MP4" 2>/dev/null | head -1)
                if [[ -f "$sample_file" ]]; then
                    local new_size=$(stat -c%s "$sample_file" 2>/dev/null || echo "0")
                    if [[ $new_size -gt 1000000 ]]; then
                        log_check_success "LFS文件已下载: $(basename "$sample_file") ($(numfmt --to=iec $new_size))"
                    fi
                fi
            else
                log_warning "Git LFS下载失败或超时，将使用备用背景"
            fi
        else
            log_warning "Git LFS未安装，无法下载视频文件"
        fi
    fi

    # 如果没有真实视频文件，创建备用背景配置
    if [[ "$has_real_videos" == "false" ]]; then
        log_info "创建备用背景配置..."
        create_fallback_backgrounds
    else
        log_success "检测到真实视频文件，视频背景应该能正常工作"
    fi

    log_success "视频背景处理完成"
}

# 创建备用背景配置
create_fallback_backgrounds() {
    log_info "生成CSS备用背景..."

    # 创建备用背景CSS文件
    cat > "$LOVE_DIR/fallback-backgrounds.css" << 'EOF'
/* 备用背景样式 - 当视频文件不可用时使用 */

/* 花朵主题背景 */
.video-background.fallback-flower {
    background: linear-gradient(135deg,
        #fce7f3 0%,
        #fbcfe8 15%,
        #f9a8d4 30%,
        #f472b6 45%,
        #ec4899 60%,
        #db2777 75%,
        #be185d 90%,
        #9d174d 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

/* 绿荫主题背景 */
.video-background.fallback-green {
    background: linear-gradient(135deg,
        #22c55e 0%,
        #16a34a 15%,
        #15803d 30%,
        #166534 45%,
        #14532d 60%,
        #052e16 75%,
        #064e3b 90%,
        #065f46 100%);
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
}

/* 星河主题背景 */
.video-background.fallback-stars {
    background: linear-gradient(135deg,
        #0c0c2e 0%,
        #1a1a3e 15%,
        #2d1b69 30%,
        #4a148c 45%,
        #6a1b9a 60%,
        #7c3aed 75%,
        #8b5cf6 90%,
        #a78bfa 100%);
    background-size: 400% 400%;
    animation: gradientShift 25s ease infinite;
}

/* 海底主题背景 */
.video-background.fallback-sea {
    background: linear-gradient(135deg,
        #0ea5e9 0%,
        #0284c7 15%,
        #0369a1 30%,
        #075985 45%,
        #0c4a6e 60%,
        #164e63 75%,
        #155e75 90%,
        #0891b2 100%);
    background-size: 400% 400%;
    animation: gradientShift 18s ease infinite;
}

/* 洱海主题背景 */
.video-background.fallback-lake {
    background: linear-gradient(135deg,
        #38bdf8 0%,
        #0ea5e9 15%,
        #0284c7 30%,
        #0369a1 45%,
        #075985 60%,
        #0c4a6e 75%,
        #164e63 90%,
        #155e75 100%);
    background-size: 400% 400%;
    animation: gradientShift 22s ease infinite;
}

/* 动画效果 */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 添加粒子效果 */
.video-background.fallback-flower::before,
.video-background.fallback-green::before,
.video-background.fallback-stars::before,
.video-background.fallback-sea::before,
.video-background.fallback-lake::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.3), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.2), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 10s linear infinite;
}

@keyframes sparkle {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}
EOF

    log_success "备用背景CSS文件已创建"

    # 创建视频背景检测脚本
    cat > "$LOVE_DIR/video-background-handler.js" << 'EOF'
// 视频背景处理脚本 - 自动检测视频加载失败并切换到备用背景
(function() {
    'use strict';

    // 页面主题映射
    const pageThemes = {
        'index.html': 'flower',
        'anniversary.html': 'green',
        'meetings.html': 'stars',
        'memorial.html': 'sea',
        'together-days.html': 'lake'
    };

    // 获取当前页面主题
    function getCurrentTheme() {
        const path = window.location.pathname;
        const filename = path.split('/').pop() || 'index.html';
        return pageThemes[filename] || 'flower';
    }

    // 应用备用背景
    function applyFallbackBackground(theme) {
        const videoContainer = document.querySelector('.video-background');
        if (videoContainer) {
            videoContainer.classList.add(`fallback-${theme}`);
            console.log(`应用${theme}主题备用背景`);
        }
    }

    // 检测视频文件是否有效
    function checkVideoFile(videoElement) {
        return new Promise((resolve) => {
            const testVideo = document.createElement('video');
            testVideo.preload = 'metadata';

            testVideo.addEventListener('loadedmetadata', function() {
                if (this.duration && this.duration > 1) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });

            testVideo.addEventListener('error', function() {
                resolve(false);
            });

            // 设置超时
            setTimeout(() => resolve(false), 5000);

            testVideo.src = videoElement.src;
        });
    }

    // 初始化视频背景处理
    async function initVideoBackgroundHandler() {
        const video = document.querySelector('.video-background video');
        if (!video) return;

        const theme = getCurrentTheme();
        console.log(`当前页面主题: ${theme}`);

        // 检查视频文件是否有效
        const isValidVideo = await checkVideoFile(video);

        if (!isValidVideo) {
            console.log('检测到无效视频文件，切换到备用背景');
            video.style.display = 'none';
            applyFallbackBackground(theme);

            // 隐藏加载遮罩
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.classList.add('hidden');
            }
            return;
        }

        // 视频文件有效，设置错误处理
        video.addEventListener('error', function() {
            console.log('视频播放出错，切换到备用背景');
            this.style.display = 'none';
            applyFallbackBackground(theme);
        });

        // 设置加载超时
        setTimeout(() => {
            if (!video.classList.contains('loaded')) {
                console.log('视频加载超时，切换到备用背景');
                video.style.display = 'none';
                applyFallbackBackground(theme);
            }
        }, 8000);
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initVideoBackgroundHandler);
    } else {
        initVideoBackgroundHandler();
    }
})();
EOF

    log_success "视频背景处理脚本已创建"
}

# 部署Love服务
deploy_love_service() {
    log_info "部署Love服务..."
    
    cd "$LOVE_DIR"
    
    # 设置manage.sh执行权限
    chmod +x manage.sh
    
    # 调用manage.sh的启动功能
    log_info "启动Love服务..."
    ./manage.sh start
    
    log_success "Love服务部署完成"
}

# 配置网络（包含New-API和Love的完整配置）
setup_network() {
    log_info "配置网络和Nginx..."
    
    cd "$LOVE_DIR"
    
    # 直接调用manage.sh的内部函数来配置网络
    log_info "配置完整的Nginx配置（包含New-API和Love）..."
    
    # 设置目录权限，让nginx可以访问
    chmod -R 755 "$LOVE_DIR"
    
    # 确保nginx用户可以访问父目录链
    WORKSPACE_DIR="$(dirname "$LOVE_DIR")"
    chmod 755 "$WORKSPACE_DIR"
    
    # 智能设置父目录权限
    CURRENT_DIR="$WORKSPACE_DIR"
    local depth_count=0
    while [[ "$CURRENT_DIR" != "/" && $depth_count -lt 3 ]]; do
        PARENT_DIR=$(dirname "$CURRENT_DIR")
        if [[ -d "$PARENT_DIR" && "$PARENT_DIR" != "/" ]]; then
            chmod o+x "$PARENT_DIR" 2>/dev/null || true
        fi
        CURRENT_DIR="$PARENT_DIR"
        ((depth_count++))
    done
    
    # 配置完整的nginx配置
    NGINX_CONFIG_SOURCE=""
    if [[ -f "config/nginx-complete.conf" ]]; then
        NGINX_CONFIG_SOURCE="config/nginx-complete.conf"
        log_info "使用Love项目的完整nginx配置"
    elif [[ -f "../shared/nginx/system-nginx.conf" ]]; then
        NGINX_CONFIG_SOURCE="../shared/nginx/system-nginx.conf"
        log_info "使用共享nginx配置作为备选"
    else
        log_warning "未找到可用的nginx配置文件，跳过网络配置"
        return 0
    fi
    
    # 复制配置文件并替换路径
    NGINX_CONFIG_TARGET="/etc/nginx/sites-available/liangliangdamowang.edu.deal.conf"
    sudo cp "$NGINX_CONFIG_SOURCE" "$NGINX_CONFIG_TARGET"
    
    # 更新nginx配置中的路径
    if grep -q "/root/workspace" "$NGINX_CONFIG_TARGET"; then
        sudo sed -i "s|/root/workspace|$WORKSPACE_DIR|g" "$NGINX_CONFIG_TARGET"
    fi
    
    # 启用配置并测试
    sudo ln -sf "$NGINX_CONFIG_TARGET" /etc/nginx/sites-enabled/
    if sudo nginx -t; then
        sudo systemctl reload nginx
        log_success "Nginx配置已部署并重新加载"
    else
        log_warning "Nginx配置测试失败，但继续执行"
    fi
    
    log_success "网络配置完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    cd "$LOVE_DIR"
    
    # 检查Love服务状态
    if systemctl is-active --quiet love-site; then
        log_success "Love Site服务运行正常"
    elif pgrep -f "node server.js" > /dev/null; then
        log_success "Love后端服务运行正常"
    else
        log_warning "Love服务未运行，但部署过程已完成"
    fi
    
    # 检查Nginx状态
    if systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
        
        # 检查配置语法
        if sudo nginx -t > /dev/null 2>&1; then
            log_success "Nginx配置语法正确"
        else
            log_warning "Nginx配置语法有问题"
        fi
    else
        log_warning "Nginx服务未运行"
    fi
    
    # 检查端口占用
    if ss -tuln | grep ":1314" > /dev/null; then
        log_success "Love API端口1314已开放"
    else
        log_warning "Love API端口1314未开放"
    fi
    
    log_success "部署验证完成"
}

# 显示完成信息
show_completion() {
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                    💕 Love Site 部署完成！ 💕                ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${CYAN}📋 访问地址:${NC}"
    echo -e "   🌐 Love网站: ${YELLOW}https://liangliangdamowang.edu.deal/love/${NC}"
    echo -e "   🔗 New-API: ${YELLOW}https://liangliangdamowang.edu.deal/${NC}"
    echo -e "   🔧 API接口: ${YELLOW}https://liangliangdamowang.edu.deal/love/api/${NC}"
    echo
    echo -e "${CYAN}🛠️ 管理命令:${NC}"
    echo -e "   • 完整管理: ${YELLOW}./manage.sh${NC}"
    echo -e "   • 查看状态: ${YELLOW}./manage.sh status${NC}"
    echo -e "   • 启动服务: ${YELLOW}./manage.sh start${NC}"
    echo -e "   • 停止服务: ${YELLOW}./manage.sh stop${NC}"
    echo -e "   • 重启服务: ${YELLOW}./manage.sh restart${NC}"
    echo -e "   • 查看日志: ${YELLOW}./manage.sh logs${NC}"
    echo
    echo -e "${CYAN}📚 文档:${NC}"
    echo -e "   • 详细文档: ${YELLOW}README.md${NC}"
    echo -e "   • 使用指南: ${YELLOW}USAGE.md${NC}"
    echo -e "   • 管理界面: ${YELLOW}./manage.sh${NC}"
    echo
    echo -e "${PURPLE}💡 提示:${NC}"
    echo -e "   • Love Site已完成部署，包含完整的网络配置"
    echo -e "   • 支持New-API和Love网站的完整功能"
    echo -e "   • 所有服务已配置开机自启动"
    echo
    echo -e "${GREEN}✅ 恭喜！您的爱情网站已成功上线！${NC}"
}

# 主函数
main() {
    print_banner
    
    # 处理命令行参数
    case "${1:-}" in
        "--help"|"-h")
            echo "Love Site 部署脚本"
            echo
            echo "用法: ./deploy.sh [选项]"
            echo
            echo "选项："
            echo "  --help, -h     显示此帮助信息"
            echo "  --quick        快速部署（跳过网络配置）"
            echo "  --network-only 仅配置网络"
            echo
            echo "示例："
            echo "  ./deploy.sh              # 完整部署"
            echo "  ./deploy.sh --quick      # 快速部署"
            echo "  ./deploy.sh --network-only # 仅配置网络"
            exit 0
            ;;
        "--quick")
            log_info "快速部署模式"
            check_environment
            install_system_dependencies
            handle_video_backgrounds
            install_dependencies
            deploy_love_service
            show_completion
            exit 0
            ;;
        "--network-only")
            log_info "仅配置网络模式"
            check_environment
            setup_network
            verify_deployment
            show_completion
            exit 0
            ;;
    esac
    
    # 完整部署流程
    check_environment
    install_system_dependencies
    handle_video_backgrounds
    install_dependencies
    deploy_love_service
    setup_network
    verify_deployment
    show_completion
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 