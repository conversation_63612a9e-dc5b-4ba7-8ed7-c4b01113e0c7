/**
 * 视频管理系统性能监控和分析报告
 * 用于生成详细的性能分析报告和优化建议
 */

(function() {
    'use strict';

    // 性能分析器
    class PerformanceAnalyzer {
        constructor() {
            this.metrics = {
                videoLoadTimes: [],
                cacheHitRates: [],
                memoryUsage: [],
                networkRequests: [],
                pageTransitions: []
            };
            
            this.startTime = performance.now();
            this.isMonitoring = false;
        }

        // 开始监控
        startMonitoring() {
            if (this.isMonitoring) return;
            
            this.isMonitoring = true;
            console.log('📊 开始性能监控...');
            
            // 监控视频加载
            this.monitorVideoLoading();
            
            // 监控内存使用
            this.monitorMemoryUsage();
            
            // 监控网络请求
            this.monitorNetworkRequests();
            
            // 监控页面切换
            this.monitorPageTransitions();
        }

        // 停止监控
        stopMonitoring() {
            this.isMonitoring = false;
            console.log('📊 停止性能监控');
        }

        // 监控视频加载性能
        monitorVideoLoading() {
            const originalLoadVideo = window.VideoManager?.loadVideo;
            if (!originalLoadVideo) return;

            const self = this;
            window.VideoManager.loadVideo = async function(pageKey, videoConfig) {
                const startTime = performance.now();
                const startMemory = self.getMemoryUsage();
                
                try {
                    const result = await originalLoadVideo.call(this, pageKey, videoConfig);
                    const endTime = performance.now();
                    const endMemory = self.getMemoryUsage();
                    
                    self.metrics.videoLoadTimes.push({
                        pageKey,
                        duration: endTime - startTime,
                        memoryDelta: endMemory - startMemory,
                        timestamp: new Date().toISOString(),
                        success: true
                    });
                    
                    return result;
                } catch (error) {
                    const endTime = performance.now();
                    
                    self.metrics.videoLoadTimes.push({
                        pageKey,
                        duration: endTime - startTime,
                        error: error.message,
                        timestamp: new Date().toISOString(),
                        success: false
                    });
                    
                    throw error;
                }
            };
        }

        // 监控内存使用
        monitorMemoryUsage() {
            const self = this;
            
            setInterval(() => {
                if (!this.isMonitoring) return;
                
                const memory = this.getMemoryUsage();
                const cacheStatus = window.VideoManager?.getCacheStatus();
                
                this.metrics.memoryUsage.push({
                    memory,
                    cacheSize: cacheStatus?.cacheSize || 0,
                    timestamp: new Date().toISOString()
                });
                
                // 只保留最近100条记录
                if (this.metrics.memoryUsage.length > 100) {
                    this.metrics.memoryUsage.shift();
                }
            }, 5000); // 每5秒记录一次
        }

        // 监控网络请求
        monitorNetworkRequests() {
            const self = this;
            
            // 监控fetch请求
            const originalFetch = window.fetch;
            window.fetch = async function(...args) {
                const startTime = performance.now();
                const url = args[0];
                
                try {
                    const response = await originalFetch.apply(this, args);
                    const endTime = performance.now();
                    
                    if (url.includes('.mp4') || url.includes('video')) {
                        self.metrics.networkRequests.push({
                            url,
                            duration: endTime - startTime,
                            success: response.ok,
                            status: response.status,
                            timestamp: new Date().toISOString()
                        });
                    }
                    
                    return response;
                } catch (error) {
                    const endTime = performance.now();
                    
                    if (url.includes('.mp4') || url.includes('video')) {
                        self.metrics.networkRequests.push({
                            url,
                            duration: endTime - startTime,
                            success: false,
                            error: error.message,
                            timestamp: new Date().toISOString()
                        });
                    }
                    
                    throw error;
                }
            };
        }

        // 监控页面切换
        monitorPageTransitions() {
            const self = this;
            let lastPage = window.location.pathname;
            
            // 监听页面变化
            const observer = new MutationObserver(() => {
                const currentPage = window.location.pathname;
                if (currentPage !== lastPage) {
                    self.metrics.pageTransitions.push({
                        from: lastPage,
                        to: currentPage,
                        timestamp: new Date().toISOString()
                    });
                    lastPage = currentPage;
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        // 获取内存使用情况
        getMemoryUsage() {
            if (performance.memory) {
                return performance.memory.usedJSHeapSize / 1024 / 1024; // MB
            }
            return 0;
        }

        // 计算缓存命中率
        calculateCacheHitRate() {
            const videoLoads = this.metrics.videoLoadTimes;
            if (videoLoads.length === 0) return 0;
            
            // 简化的缓存命中率计算：加载时间小于100ms认为是缓存命中
            const cacheHits = videoLoads.filter(load => load.success && load.duration < 100).length;
            return (cacheHits / videoLoads.length) * 100;
        }

        // 生成性能报告
        generateReport() {
            const report = {
                summary: this.generateSummary(),
                videoPerformance: this.analyzeVideoPerformance(),
                memoryAnalysis: this.analyzeMemoryUsage(),
                networkAnalysis: this.analyzeNetworkRequests(),
                recommendations: this.generateRecommendations(),
                timestamp: new Date().toISOString(),
                monitoringDuration: performance.now() - this.startTime
            };
            
            return report;
        }

        // 生成摘要
        generateSummary() {
            const totalVideoLoads = this.metrics.videoLoadTimes.length;
            const successfulLoads = this.metrics.videoLoadTimes.filter(load => load.success).length;
            const averageLoadTime = this.metrics.videoLoadTimes.reduce((sum, load) => sum + load.duration, 0) / totalVideoLoads || 0;
            const cacheHitRate = this.calculateCacheHitRate();
            const currentMemory = this.getMemoryUsage();
            
            return {
                totalVideoLoads,
                successfulLoads,
                successRate: totalVideoLoads > 0 ? (successfulLoads / totalVideoLoads) * 100 : 0,
                averageLoadTime: averageLoadTime.toFixed(2),
                cacheHitRate: cacheHitRate.toFixed(1),
                currentMemoryUsage: currentMemory.toFixed(2),
                pageTransitions: this.metrics.pageTransitions.length
            };
        }

        // 分析视频性能
        analyzeVideoPerformance() {
            const loads = this.metrics.videoLoadTimes;
            if (loads.length === 0) return { message: '暂无视频加载数据' };
            
            const successful = loads.filter(load => load.success);
            const failed = loads.filter(load => !load.success);
            
            const avgSuccessTime = successful.reduce((sum, load) => sum + load.duration, 0) / successful.length || 0;
            const maxLoadTime = Math.max(...successful.map(load => load.duration));
            const minLoadTime = Math.min(...successful.map(load => load.duration));
            
            return {
                totalLoads: loads.length,
                successfulLoads: successful.length,
                failedLoads: failed.length,
                averageLoadTime: avgSuccessTime.toFixed(2),
                maxLoadTime: maxLoadTime.toFixed(2),
                minLoadTime: minLoadTime.toFixed(2),
                loadsByPage: this.groupLoadsByPage(loads)
            };
        }

        // 按页面分组加载数据
        groupLoadsByPage(loads) {
            const grouped = {};
            loads.forEach(load => {
                if (!grouped[load.pageKey]) {
                    grouped[load.pageKey] = {
                        count: 0,
                        totalTime: 0,
                        successes: 0,
                        failures: 0
                    };
                }
                
                grouped[load.pageKey].count++;
                grouped[load.pageKey].totalTime += load.duration;
                
                if (load.success) {
                    grouped[load.pageKey].successes++;
                } else {
                    grouped[load.pageKey].failures++;
                }
            });
            
            // 计算平均时间
            Object.keys(grouped).forEach(page => {
                grouped[page].averageTime = (grouped[page].totalTime / grouped[page].count).toFixed(2);
            });
            
            return grouped;
        }

        // 分析内存使用
        analyzeMemoryUsage() {
            const usage = this.metrics.memoryUsage;
            if (usage.length === 0) return { message: '暂无内存使用数据' };
            
            const memories = usage.map(u => u.memory);
            const avgMemory = memories.reduce((sum, mem) => sum + mem, 0) / memories.length;
            const maxMemory = Math.max(...memories);
            const minMemory = Math.min(...memories);
            
            return {
                samples: usage.length,
                averageMemory: avgMemory.toFixed(2),
                maxMemory: maxMemory.toFixed(2),
                minMemory: minMemory.toFixed(2),
                memoryTrend: this.calculateMemoryTrend(memories)
            };
        }

        // 计算内存趋势
        calculateMemoryTrend(memories) {
            if (memories.length < 2) return 'stable';
            
            const first = memories[0];
            const last = memories[memories.length - 1];
            const change = ((last - first) / first) * 100;
            
            if (change > 10) return 'increasing';
            if (change < -10) return 'decreasing';
            return 'stable';
        }

        // 分析网络请求
        analyzeNetworkRequests() {
            const requests = this.metrics.networkRequests;
            if (requests.length === 0) return { message: '暂无网络请求数据' };
            
            const successful = requests.filter(req => req.success);
            const failed = requests.filter(req => !req.success);
            
            const avgRequestTime = successful.reduce((sum, req) => sum + req.duration, 0) / successful.length || 0;
            
            return {
                totalRequests: requests.length,
                successfulRequests: successful.length,
                failedRequests: failed.length,
                averageRequestTime: avgRequestTime.toFixed(2),
                successRate: requests.length > 0 ? (successful.length / requests.length) * 100 : 0
            };
        }

        // 生成优化建议
        generateRecommendations() {
            const recommendations = [];
            const summary = this.generateSummary();
            
            // 基于成功率的建议
            if (summary.successRate < 90) {
                recommendations.push({
                    type: 'error_handling',
                    priority: 'high',
                    message: `视频加载成功率较低(${summary.successRate.toFixed(1)}%)，建议检查网络连接和视频文件可用性`
                });
            }
            
            // 基于加载时间的建议
            if (parseFloat(summary.averageLoadTime) > 3000) {
                recommendations.push({
                    type: 'performance',
                    priority: 'medium',
                    message: `平均加载时间较长(${summary.averageLoadTime}ms)，建议优化视频文件大小或使用CDN`
                });
            }
            
            // 基于缓存命中率的建议
            if (parseFloat(summary.cacheHitRate) < 50) {
                recommendations.push({
                    type: 'caching',
                    priority: 'medium',
                    message: `缓存命中率较低(${summary.cacheHitRate}%)，建议检查缓存策略和预加载机制`
                });
            }
            
            // 基于内存使用的建议
            if (parseFloat(summary.currentMemoryUsage) > 100) {
                recommendations.push({
                    type: 'memory',
                    priority: 'high',
                    message: `内存使用较高(${summary.currentMemoryUsage}MB)，建议检查内存泄漏和缓存清理机制`
                });
            }
            
            return recommendations;
        }

        // 输出报告到控制台
        printReport() {
            const report = this.generateReport();
            
            console.log('\n📊 视频管理系统性能报告');
            console.log('='.repeat(50));
            
            console.log('\n📈 性能摘要:');
            console.log(`视频加载次数: ${report.summary.totalVideoLoads}`);
            console.log(`成功率: ${report.summary.successRate.toFixed(1)}%`);
            console.log(`平均加载时间: ${report.summary.averageLoadTime}ms`);
            console.log(`缓存命中率: ${report.summary.cacheHitRate}%`);
            console.log(`当前内存使用: ${report.summary.currentMemoryUsage}MB`);
            console.log(`页面切换次数: ${report.summary.pageTransitions}`);
            
            console.log('\n🎬 视频性能分析:');
            console.log(report.videoPerformance);
            
            console.log('\n🧠 内存使用分析:');
            console.log(report.memoryAnalysis);
            
            console.log('\n🌐 网络请求分析:');
            console.log(report.networkAnalysis);
            
            console.log('\n💡 优化建议:');
            report.recommendations.forEach((rec, index) => {
                console.log(`${index + 1}. [${rec.priority.toUpperCase()}] ${rec.message}`);
            });
            
            console.log('\n' + '='.repeat(50));
            
            return report;
        }
    }

    // 导出到全局
    window.PerformanceAnalyzer = PerformanceAnalyzer;

    // 创建全局实例
    window.performanceAnalyzer = new PerformanceAnalyzer();

    console.log('📊 性能分析器已加载');
    console.log('💡 使用方法:');
    console.log('   window.performanceAnalyzer.startMonitoring(); // 开始监控');
    console.log('   window.performanceAnalyzer.printReport(); // 生成报告');
    console.log('   window.performanceAnalyzer.stopMonitoring(); // 停止监控');

})();
