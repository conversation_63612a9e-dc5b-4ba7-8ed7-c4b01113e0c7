#!/bin/bash

# 批量更新所有HTML页面，将enhanced-video-manager.js替换为simple-video-manager.js

echo "🔄 开始批量更新HTML页面..."

# 定义要更新的文件列表
files=(
    "html/meetings.html"
    "html/memorial.html"
    "html/together-days.html"
)

# 遍历每个文件并进行替换
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "📝 更新文件: $file"
        
        # 替换enhanced-video-manager.js为simple-video-manager.js
        sed -i 's/enhanced-video-manager\.js/simple-video-manager.js/g' "$file"
        
        echo "✅ 完成: $file"
    else
        echo "⚠️ 文件不存在: $file"
    fi
done

echo "🎉 批量更新完成！"
echo ""
echo "📋 更新内容："
echo "- 将 enhanced-video-manager.js 替换为 simple-video-manager.js"
echo "- 所有页面现在使用简化的视频管理器"
echo ""
echo "🚀 下一步："
echo "1. 测试每个页面的视频加载"
echo "2. 检查控制台是否有错误"
echo "3. 确认视频能正常播放"
