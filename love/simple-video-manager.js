/**
 * Simple Video Manager - 简单直接的视频管理器
 * 完全删除复杂优化策略，直接加载视频
 */

class SimpleVideoManager {
    constructor() {
        console.log('🚀 Simple Video Manager initialized - Direct loading mode');
        this.currentVideo = null;
    }

    /**
     * 直接加载视频 - 无任何优化策略
     */
    async loadVideo(pageKey, videoConfig) {
        console.log(`🎬 Loading video for ${pageKey}:`, videoConfig.name);
        console.log(`📹 Video URL:`, videoConfig.url);
        
        try {
            // 创建视频元素
            const video = document.createElement('video');
            video.autoplay = true;
            video.muted = true;
            video.loop = true;
            video.playsInline = true;
            video.preload = 'auto';
            
            // 直接设置视频源 - 不使用任何优化版本
            video.src = videoConfig.url;
            
            // 等待视频加载
            await this.waitForVideoLoad(video);
            
            this.currentVideo = video;
            console.log(`✅ Video loaded successfully for ${pageKey}`);
            return video;
            
        } catch (error) {
            console.error(`❌ Failed to load video for ${pageKey}:`, error);
            throw error;
        }
    }

    /**
     * 等待视频加载完成
     */
    waitForVideoLoad(video) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Video load timeout after 30 seconds'));
            }, 30000);

            video.addEventListener('loadeddata', () => {
                clearTimeout(timeout);
                console.log('✅ Video loadeddata event fired');
                resolve(video);
            });

            video.addEventListener('error', (e) => {
                clearTimeout(timeout);
                console.error('❌ Video error event:', e);
                reject(new Error(`Video load error: ${e.message || 'Unknown error'}`));
            });

            // 开始加载
            video.load();
        });
    }

    /**
     * 预加载视频（简化版）
     */
    async preloadVideo(pageKey, videoConfig) {
        console.log(`🔄 Preloading video for ${pageKey}:`, videoConfig.name);
        try {
            await this.loadVideo(pageKey, videoConfig);
        } catch (error) {
            console.log(`⚠️ Preload failed for ${pageKey}:`, error);
        }
    }

    /**
     * 应用备用背景
     */
    applyFallbackBackground(theme) {
        console.log(`🎨 Applying fallback background: ${theme}`);
        
        const fallbackConfig = window.CONFIG?.VIDEOS?.FALLBACK_THEMES?.[theme];
        if (!fallbackConfig) {
            console.error(`❌ Fallback theme not found: ${theme}`);
            return;
        }

        const videoContainer = document.querySelector('.video-background');
        if (videoContainer) {
            videoContainer.style.background = fallbackConfig.gradient;
            videoContainer.style.animation = fallbackConfig.animation;
            videoContainer.classList.add('fallback-active');
        }
    }

    /**
     * 暂停所有视频
     */
    pauseAllVideos() {
        if (this.currentVideo && !this.currentVideo.paused) {
            this.currentVideo.pause();
            console.log('⏸️ Paused current video');
        }
    }

    /**
     * 恢复当前视频
     */
    resumeCurrentVideo() {
        if (this.currentVideo && this.currentVideo.paused) {
            this.currentVideo.play().catch(e => {
                console.log('⚠️ Failed to resume video:', e);
            });
            console.log('▶️ Resumed current video');
        }
    }

    /**
     * 获取缓存状态
     */
    getCacheStatus() {
        return {
            currentVideo: this.currentVideo ? 'loaded' : 'none',
            videoSrc: this.currentVideo ? this.currentVideo.src : 'none'
        };
    }

    /**
     * 获取当前页面键（兼容方法）
     */
    getCurrentPageKey() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index.html') return 'INDEX';
        if (path === '/meetings' || path === '/meetings.html') return 'MEETINGS';
        if (path === '/anniversary' || path === '/anniversary.html') return 'ANNIVERSARY';
        if (path === '/memorial' || path === '/memorial.html') return 'MEMORIAL';
        if (path === '/together-days' || path === '/together-days.html') return 'TOGETHER_DAYS';
        return 'INDEX';
    }
}

// 创建全局实例
window.VideoManager = new SimpleVideoManager();

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleVideoManager;
}

console.log('📦 Simple Video Manager loaded and ready');
